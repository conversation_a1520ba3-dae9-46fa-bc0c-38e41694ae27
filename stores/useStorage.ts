import { create } from 'zustand';
import {
  NASVlan,
  NASRole,
  User,
  CustomerInfo,
  PackageInfo,
  Group,
  NAS,
  Voucher,
  Analytics,
  DepartmentStats,
  RadAcctSession

} from '@/types/interface-type';
import apiClient from '@/lib/apiClient';

type DataStore = {
  // Data arrays
  users: User[];
  customers: CustomerInfo[];
  externalCustomers: CustomerInfo[]; // For /ecustomer endpoint
  packages: PackageInfo[];
  groups: Group[];
  nasDevices: NAS[];
  nasVlan: NASVlan[];
  nasRole: NASRole[];
  vouchers: Voucher[];
  analytics: Analytics[];
  departmentStats: DepartmentStats[];
  sessions: RadAcctSession[];
  postAuthData: any[]; // For /auth/postauth/ endpoint

  // Loading states
  loading: boolean;
  loadingUsers: boolean;
  loadingCustomers: boolean;
  loadingExternalCustomers: boolean;
  loadingPackages: boolean;
  loadingGroups: boolean;
  loadingNasDevices: boolean;
  loadingVouchers: boolean;
  loadingAnalytics: boolean;

  // Fetch status flags
  usersFetched: boolean;
  customersFetched: boolean;
  externalCustomersFetched: boolean;
  packagesFetched: boolean;
  groupsFetched: boolean;
  nasDevicesFetched: boolean;
  nasVlanFetched: boolean;
  nasRoleFetched: boolean;
  vouchersFetched: boolean;
  analyticsFetched: boolean;
  postAuthFetched: boolean;

  // Error states
  usersError: string | null;
  customersError: string | null;
  externalCustomersError: string | null;
  packagesError: string | null;
  groupsError: string | null;
  nasDevicesError: string | null;
  vouchersError: string | null;
  analyticsError: string | null;

  // Setter functions
  setUsers: (users: User[]) => void;
  setCustomers: (customers: CustomerInfo[]) => void;
  setExternalCustomers: (customers: CustomerInfo[]) => void;
  setPackages: (packages: PackageInfo[]) => void;
  setGroups: (groups: Group[]) => void;
  setNasDevices: (devices: NAS[]) => void;
  setNasVlan: (nasVlan: NASVlan[]) => void;
  setNasRole: (nasRole: NASRole[]) => void;
  setVouchers: (vouchers: Voucher[]) => void;
  setAnalytics: (analytics: Analytics[]) => void;
  setDepartmentStats: (stats: DepartmentStats[]) => void;
  setSessions: (sessions: RadAcctSession[]) => void;
  setPostAuthData: (data: any[]) => void;
  setLoading: (state: boolean) => void;

  // Fetch functions
  fetchUsers: () => Promise<void>;
  fetchCustomers: () => Promise<void>;
  fetchExternalCustomers: () => Promise<void>;
  fetchPackages: () => Promise<void>;
  fetchGroups: () => Promise<void>;
  fetchNasDevices: () => Promise<void>;
  fetchNasVlans: () => Promise<void>;
  fetchNasRoles: () => Promise<void>;
  fetchVouchers: () => Promise<void>;
  fetchAnalytics: (params?: any) => Promise<void>;
  fetchDepartmentStats: (params?: any) => Promise<void>;
  fetchSessions: (endpoint: string) => Promise<void>;
  fetchPostAuthData: () => Promise<void>;

  // CREATE operations (POST)
  createUser: (userData: any) => Promise<any>;
  createCustomer: (customerData: any) => Promise<any>;
  createExternalCustomer: (customerData: any) => Promise<any>;
  createPackage: (packageData: any) => Promise<any>;
  createGroup: (groupData: any) => Promise<any>;
  createNasDevice: (nasData: any) => Promise<any>;
  createNasRole: (roleData: any) => Promise<any>;
  createNasVlan: (vlanData: any) => Promise<any>;
  createVoucher: (voucherData: any) => Promise<any>;

  // UPDATE operations (PATCH)
  updateUser: (id: number, userData: any) => Promise<any>;
  updateCustomer: (id: number, customerData: any) => Promise<any>;
  updatePackage: (id: number, packageData: any) => Promise<any>;
  updateGroup: (groupname: string, groupData: any) => Promise<any>;
  updateNasDevice: (id: number, nasData: any) => Promise<any>;
  updateNasRole: (id: number, roleData: any) => Promise<any>;
  updateNasVlan: (id: number, vlanData: any) => Promise<any>;

  // DELETE operations
  deleteUser: (id: number) => Promise<void>;
  deleteCustomer: (id: number) => Promise<void>;
  deletePackage: (id: number) => Promise<void>;
  deleteGroup: (groupname: string) => Promise<void>;
  deleteNasDevice: (id: number) => Promise<void>;
  deleteNasRole: (id: number) => Promise<void>;
  deleteNasVlan: (id: number) => Promise<void>;

  // Special operations
  grantGuestAccess: (grantData: any) => Promise<any>;
  requestGuestAccess: (requestData: any) => Promise<any>;
  getCustomerStats: (username: string) => Promise<any>;

  // Utility functions
  clearErrors: () => void;
  resetFetchFlags: () => void;
};

export const useDataStore = create<DataStore>((set, get) => ({
  // Initial data arrays
  users: [],
  customers: [],
  externalCustomers: [],
  packages: [],
  groups: [],
  nasDevices: [],
  nasVlan: [],
  nasRole: [],
  vouchers: [],
  analytics: [],
  departmentStats: [],
  sessions: [],
  postAuthData: [],

  // Initial loading states
  loading: false,
  loadingUsers: false,
  loadingCustomers: false,
  loadingExternalCustomers: false,
  loadingPackages: false,
  loadingGroups: false,
  loadingNasDevices: false,
  loadingVouchers: false,
  loadingAnalytics: false,

  // Initial fetch status flags
  usersFetched: false,
  customersFetched: false,
  externalCustomersFetched: false,
  packagesFetched: false,
  groupsFetched: false,
  nasDevicesFetched: false,
  nasVlanFetched: false,
  nasRoleFetched: false,
  vouchersFetched: false,
  analyticsFetched: false,
  postAuthFetched: false,

  // Initial error states
  usersError: null,
  customersError: null,
  externalCustomersError: null,
  packagesError: null,
  groupsError: null,
  nasDevicesError: null,
  vouchersError: null,
  analyticsError: null,

  // Setter functions
  setUsers: (users) => set({ users }),
  setCustomers: (customers) => set({ customers }),
  setExternalCustomers: (customers) => set({ externalCustomers: customers }),
  setPackages: (packages) => set({ packages }),
  setGroups: (groups) => set({ groups }),
  setNasDevices: (devices) => set({ nasDevices: devices }),
  setNasVlan: (nasVlan) => set({ nasVlan }),
  setNasRole: (nasRole) => set({ nasRole }),
  setVouchers: (vouchers) => set({ vouchers }),
  setAnalytics: (analytics) => set({ analytics }),
  setDepartmentStats: (stats) => set({ departmentStats: stats }),
  setSessions: (sessions) => set({ sessions }),
  setPostAuthData: (data) => set({ postAuthData: data }),
  setLoading: (loading) => set({ loading }),

  // Fetch functions
  fetchUsers: async () => {
    if (get().usersFetched) return;
    set({ loadingUsers: true, usersError: null });
    try {
      const res = await apiClient.get('/user');
      const data = res?.data || [];
      set({ users: data, usersFetched: true });
    } catch (err: any) {
      console.error('Failed to fetch users:', err);
      set({ usersError: err.message || 'Failed to fetch users', usersFetched: true });
    } finally {
      set({ loadingUsers: false });
    }
  },

  fetchCustomers: async () => {
    if (get().customersFetched) return;
    set({ loadingCustomers: true, customersError: null });
    try {
      const res = await apiClient.get('/customer');
      const data = res?.data || [];
      set({ customers: data, customersFetched: true });
    } catch (err: any) {
      console.error('Failed to fetch customers:', err);
      set({ customersError: err.message || 'Failed to fetch customers', customersFetched: true });
    } finally {
      set({ loadingCustomers: false });
    }
  },

  fetchPackages: async () => {
    if (get().packagesFetched) return;
    set({ loadingPackages: true, packagesError: null });
    try {
      const res = await apiClient.get('/package');
      const data = res?.data || [];
      set({ packages: data, packagesFetched: true });
    } catch (err: any) {
      console.error('Failed to fetch packages:', err);
      set({ packagesError: err.message || 'Failed to fetch packages', packagesFetched: true });
    } finally {
      set({ loadingPackages: false });
    }
  },

  fetchGroups: async () => {
    if (get().groupsFetched) return;
    set({ loadingGroups: true, groupsError: null });
    try {
      const res = await apiClient.get('/groups');
      const data = res?.data || [];
      set({ groups: data, groupsFetched: true });
    } catch (err: any) {
      console.error('Failed to fetch groups:', err);
      set({ groupsError: err.message || 'Failed to fetch groups', groupsFetched: true });
    } finally {
      set({ loadingGroups: false });
    }
  },

  fetchNasDevices: async () => {
    if (get().nasDevicesFetched) return;
    set({ loadingNasDevices: true, nasDevicesError: null });
    try {
      const res = await apiClient.get('/nas');
      const data = res?.data || [];
      set({ nasDevices: data, nasDevicesFetched: true });
    } catch (err: any) {
      console.error('Failed to fetch NAS devices:', err);
      set({ nasDevicesError: err.message || 'Failed to fetch NAS devices', nasDevicesFetched: true });
    } finally {
      set({ loadingNasDevices: false });
    }
  },

  fetchNasVlans: async () => {
    if (get().nasVlanFetched) return;
    set({ loading: true });
    try {
      const res = await apiClient.get('/nas/vlan');
      const data = res?.data || [];
      set({ nasVlan: data, nasVlanFetched: true });
    } catch (err: any) {
      console.error('Failed to fetch NAS VLANs:', err);
      set({ nasVlanFetched: true });
    } finally {
      set({ loading: false });
    }
  },

  fetchNasRoles: async () => {
    if (get().nasRoleFetched) return;
    set({ loading: true });
    try {
      const res = await apiClient.get('/nas/role');
      const data = res?.data || [];
      set({ nasRole: data, nasRoleFetched: true });
    } catch (err: any) {
      console.error('Failed to fetch NAS Roles:', err);
      set({ nasRoleFetched: true });
    } finally {
      set({ loading: false });
    }
  },

  fetchVouchers: async () => {
    if (get().vouchersFetched) return;
    set({ loadingVouchers: true, vouchersError: null });
    try {
      const res = await apiClient.get('/guest/voucher/?type=guest');
      const data = res?.data?.data || [];
      set({ vouchers: data, vouchersFetched: true });
    } catch (err: any) {
      console.error('Failed to fetch vouchers:', err);
      set({ vouchersError: err.message || 'Failed to fetch vouchers', vouchersFetched: true });
    } finally {
      set({ loadingVouchers: false });
    }
  },

  fetchAnalytics: async (params = {}) => {
    set({ loadingAnalytics: true, analyticsError: null });
    try {
      const res = await apiClient.post('/analytics', params);
      const data = res?.data?.data || [];
      set({ analytics: data, analyticsFetched: true });
    } catch (err: any) {
      console.error('Failed to fetch analytics:', err);
      set({ analyticsError: err.message || 'Failed to fetch analytics' });
    } finally {
      set({ loadingAnalytics: false });
    }
  },

  fetchDepartmentStats: async (params = {}) => {
    set({ loadingAnalytics: true, analyticsError: null });
    try {
      const res = await apiClient.post('/department/stats', params);
      const data = res?.data || [];
      set({ departmentStats: data });
    } catch (err: any) {
      console.error('Failed to fetch department stats:', err);
      set({ analyticsError: err.message || 'Failed to fetch department stats' });
    } finally {
      set({ loadingAnalytics: false });
    }
  },

  fetchSessions: async (endpoint: string) => {
    set({ loading: true });
    try {
      const res = await apiClient.get(endpoint);
      const data = res?.data || [];
      set({ sessions: data });
    } catch (err: any) {
      console.error('Failed to fetch sessions:', err);
    } finally {
      set({ loading: false });
    }
  },

  fetchExternalCustomers: async () => {
    if (get().externalCustomersFetched) return;

    set({ loadingExternalCustomers: true, externalCustomersError: null });
    try {
      const res = await apiClient.get('/ecustomer');
      const data = res?.data || [];
      set({ externalCustomers: data, externalCustomersFetched: true });
    } catch (err: any) {
      console.error('Failed to fetch external customers:', err);
      set({ externalCustomersError: err.message || 'Failed to fetch external customers' });
    } finally {
      set({ loadingExternalCustomers: false });
    }
  },

  fetchPostAuthData: async () => {
    if (get().postAuthFetched) return;

    set({ loading: true });
    try {
      const res = await apiClient.get('/auth/postauth/');
      const data = res?.data || [];
      set({ postAuthData: data, postAuthFetched: true });
    } catch (err: any) {
      console.error('Failed to fetch post auth data:', err);
    } finally {
      set({ loading: false });
    }
  },

  // Utility functions
  clearErrors: () => set({
    usersError: null,
    customersError: null,
    externalCustomersError: null,
    packagesError: null,
    groupsError: null,
    nasDevicesError: null,
    vouchersError: null,
    analyticsError: null,
  }),

  resetFetchFlags: () => set({
    usersFetched: false,
    customersFetched: false,
    externalCustomersFetched: false,
    packagesFetched: false,
    groupsFetched: false,
    nasDevicesFetched: false,
    nasVlanFetched: false,
    nasRoleFetched: false,
    vouchersFetched: false,
    analyticsFetched: false,
    postAuthFetched: false,
  }),

  // CREATE operations (POST)
  createUser: async (userData: any) => {
    set({ loadingUsers: true, usersError: null });
    try {
      const res = await apiClient.post('/user', userData);
      // Refresh users list after creation
      set({ usersFetched: false });
      get().fetchUsers();
      return res.data;
    } catch (err: any) {
      console.error('Failed to create user:', err);
      set({ usersError: err.message || 'Failed to create user' });
      throw err;
    } finally {
      set({ loadingUsers: false });
    }
  },

  createCustomer: async (customerData: any) => {
    set({ loadingCustomers: true, customersError: null });
    try {
      const res = await apiClient.post('/customer', customerData);
      // Refresh customers list after creation
      set({ customersFetched: false });
      get().fetchCustomers();
      return res.data;
    } catch (err: any) {
      console.error('Failed to create customer:', err);
      set({ customersError: err.message || 'Failed to create customer' });
      throw err;
    } finally {
      set({ loadingCustomers: false });
    }
  },

  createExternalCustomer: async (customerData: any) => {
    set({ loadingExternalCustomers: true, externalCustomersError: null });
    try {
      const res = await apiClient.post('/ecustomer', customerData);
      // Refresh external customers list after creation
      set({ externalCustomersFetched: false });
      get().fetchExternalCustomers();
      return res.data;
    } catch (err: any) {
      console.error('Failed to create external customer:', err);
      set({ externalCustomersError: err.message || 'Failed to create external customer' });
      throw err;
    } finally {
      set({ loadingExternalCustomers: false });
    }
  },

  createPackage: async (packageData: any) => {
    set({ loadingPackages: true, packagesError: null });
    try {
      const res = await apiClient.post('/package', packageData);
      // Refresh packages list after creation
      set({ packagesFetched: false });
      get().fetchPackages();
      return res.data;
    } catch (err: any) {
      console.error('Failed to create package:', err);
      set({ packagesError: err.message || 'Failed to create package' });
      throw err;
    } finally {
      set({ loadingPackages: false });
    }
  },

  createGroup: async (groupData: any) => {
    set({ loadingGroups: true, groupsError: null });
    try {
      const res = await apiClient.post('/group', groupData);
      // Refresh groups list after creation
      set({ groupsFetched: false });
      get().fetchGroups();
      return res.data;
    } catch (err: any) {
      console.error('Failed to create group:', err);
      set({ groupsError: err.message || 'Failed to create group' });
      throw err;
    } finally {
      set({ loadingGroups: false });
    }
  },

  createNasDevice: async (nasData: any) => {
    set({ loadingNasDevices: true, nasDevicesError: null });
    try {
      const res = await apiClient.post('/nas', nasData);
      // Refresh NAS devices list after creation
      set({ nasDevicesFetched: false });
      get().fetchNasDevices();
      return res.data;
    } catch (err: any) {
      console.error('Failed to create NAS device:', err);
      set({ nasDevicesError: err.message || 'Failed to create NAS device' });
      throw err;
    } finally {
      set({ loadingNasDevices: false });
    }
  },

  createNasRole: async (roleData: any) => {
    set({ loading: true });
    try {
      const res = await apiClient.post('/nas/role', roleData);
      // Refresh NAS roles list after creation
      set({ nasRoleFetched: false });
      get().fetchNasRoles();
      return res.data;
    } catch (err: any) {
      console.error('Failed to create NAS role:', err);
      throw err;
    } finally {
      set({ loading: false });
    }
  },

  createNasVlan: async (vlanData: any) => {
    set({ loading: true });
    try {
      const res = await apiClient.post('/nas/vlan', vlanData);
      // Refresh NAS VLANs list after creation
      set({ nasVlanFetched: false });
      get().fetchNasVlans();
      return res.data;
    } catch (err: any) {
      console.error('Failed to create NAS VLAN:', err);
      throw err;
    } finally {
      set({ loading: false });
    }
  },

  createVoucher: async (voucherData: any) => {
    set({ loadingVouchers: true, vouchersError: null });
    try {
      const res = await apiClient.post('/guest/voucher', voucherData);
      // Refresh vouchers list after creation
      set({ vouchersFetched: false });
      get().fetchVouchers();
      return res.data;
    } catch (err: any) {
      console.error('Failed to create voucher:', err);
      set({ vouchersError: err.message || 'Failed to create voucher' });
      throw err;
    } finally {
      set({ loadingVouchers: false });
    }
  },

  // UPDATE operations (PATCH)
  updateUser: async (id: number, userData: any) => {
    set({ loadingUsers: true, usersError: null });
    try {
      const res = await apiClient.patch(`/user/${id}`, userData);
      // Refresh users list after update
      set({ usersFetched: false });
      get().fetchUsers();
      return res.data;
    } catch (err: any) {
      console.error('Failed to update user:', err);
      set({ usersError: err.message || 'Failed to update user' });
      throw err;
    } finally {
      set({ loadingUsers: false });
    }
  },

  updateCustomer: async (id: number, customerData: any) => {
    set({ loadingCustomers: true, customersError: null });
    try {
      const res = await apiClient.patch(`/customer/${id}`, customerData);
      // Refresh customers list after update
      set({ customersFetched: false });
      get().fetchCustomers();
      return res.data;
    } catch (err: any) {
      console.error('Failed to update customer:', err);
      set({ customersError: err.message || 'Failed to update customer' });
      throw err;
    } finally {
      set({ loadingCustomers: false });
    }
  },

  updatePackage: async (id: number, packageData: any) => {
    set({ loadingPackages: true, packagesError: null });
    try {
      const res = await apiClient.patch(`/package/${id}`, packageData);
      // Refresh packages list after update
      set({ packagesFetched: false });
      get().fetchPackages();
      return res.data;
    } catch (err: any) {
      console.error('Failed to update package:', err);
      set({ packagesError: err.message || 'Failed to update package' });
      throw err;
    } finally {
      set({ loadingPackages: false });
    }
  },

  updateGroup: async (groupname: string, groupData: any) => {
    set({ loadingGroups: true, groupsError: null });
    try {
      const res = await apiClient.patch(`/group/${groupname}`, groupData);
      // Refresh groups list after update
      set({ groupsFetched: false });
      get().fetchGroups();
      return res.data;
    } catch (err: any) {
      console.error('Failed to update group:', err);
      set({ groupsError: err.message || 'Failed to update group' });
      throw err;
    } finally {
      set({ loadingGroups: false });
    }
  },

  updateNasDevice: async (id: number, nasData: any) => {
    set({ loadingNasDevices: true, nasDevicesError: null });
    try {
      const res = await apiClient.patch(`/nas/${id}`, nasData);
      // Refresh NAS devices list after update
      set({ nasDevicesFetched: false });
      get().fetchNasDevices();
      return res.data;
    } catch (err: any) {
      console.error('Failed to update NAS device:', err);
      set({ nasDevicesError: err.message || 'Failed to update NAS device' });
      throw err;
    } finally {
      set({ loadingNasDevices: false });
    }
  },

  updateNasRole: async (id: number, roleData: any) => {
    set({ loading: true });
    try {
      const res = await apiClient.patch(`/nas/role/${id}`, roleData);
      // Refresh NAS roles list after update
      set({ nasRoleFetched: false });
      get().fetchNasRoles();
      return res.data;
    } catch (err: any) {
      console.error('Failed to update NAS role:', err);
      throw err;
    } finally {
      set({ loading: false });
    }
  },

  updateNasVlan: async (id: number, vlanData: any) => {
    set({ loading: true });
    try {
      const res = await apiClient.patch(`/nas/vlan/${id}`, vlanData);
      // Refresh NAS VLANs list after update
      set({ nasVlanFetched: false });
      get().fetchNasVlans();
      return res.data;
    } catch (err: any) {
      console.error('Failed to update NAS VLAN:', err);
      throw err;
    } finally {
      set({ loading: false });
    }
  },

  // DELETE operations
  deleteUser: async (id: number) => {
    set({ loadingUsers: true, usersError: null });
    try {
      await apiClient.delete(`/user/${id}`);
      // Refresh users list after deletion
      set({ usersFetched: false });
      get().fetchUsers();
    } catch (err: any) {
      console.error('Failed to delete user:', err);
      set({ usersError: err.message || 'Failed to delete user' });
      throw err;
    } finally {
      set({ loadingUsers: false });
    }
  },

  deleteCustomer: async (id: number) => {
    set({ loadingCustomers: true, customersError: null });
    try {
      await apiClient.delete(`/customer/${id}`);
      // Refresh customers list after deletion
      set({ customersFetched: false });
      get().fetchCustomers();
    } catch (err: any) {
      console.error('Failed to delete customer:', err);
      set({ customersError: err.message || 'Failed to delete customer' });
      throw err;
    } finally {
      set({ loadingCustomers: false });
    }
  },

  deletePackage: async (id: number) => {
    set({ loadingPackages: true, packagesError: null });
    try {
      await apiClient.delete(`/package/${id}`);
      // Refresh packages list after deletion
      set({ packagesFetched: false });
      get().fetchPackages();
    } catch (err: any) {
      console.error('Failed to delete package:', err);
      set({ packagesError: err.message || 'Failed to delete package' });
      throw err;
    } finally {
      set({ loadingPackages: false });
    }
  },

  deleteGroup: async (groupname: string) => {
    set({ loadingGroups: true, groupsError: null });
    try {
      await apiClient.delete(`/group/${groupname}`);
      // Refresh groups list after deletion
      set({ groupsFetched: false });
      get().fetchGroups();
    } catch (err: any) {
      console.error('Failed to delete group:', err);
      set({ groupsError: err.message || 'Failed to delete group' });
      throw err;
    } finally {
      set({ loadingGroups: false });
    }
  },

  deleteNasDevice: async (id: number) => {
    set({ loadingNasDevices: true, nasDevicesError: null });
    try {
      await apiClient.delete(`/nas/${id}`);
      // Refresh NAS devices list after deletion
      set({ nasDevicesFetched: false });
      get().fetchNasDevices();
    } catch (err: any) {
      console.error('Failed to delete NAS device:', err);
      set({ nasDevicesError: err.message || 'Failed to delete NAS device' });
      throw err;
    } finally {
      set({ loadingNasDevices: false });
    }
  },

  deleteNasRole: async (id: number) => {
    set({ loading: true });
    try {
      await apiClient.delete(`/nas/role/${id}`);
      // Refresh NAS roles list after deletion
      set({ nasRoleFetched: false });
      get().fetchNasRoles();
    } catch (err: any) {
      console.error('Failed to delete NAS role:', err);
      throw err;
    } finally {
      set({ loading: false });
    }
  },

  deleteNasVlan: async (id: number) => {
    set({ loading: true });
    try {
      await apiClient.delete(`/nas/vlan/${id}`);
      // Refresh NAS VLANs list after deletion
      set({ nasVlanFetched: false });
      get().fetchNasVlans();
    } catch (err: any) {
      console.error('Failed to delete NAS VLAN:', err);
      throw err;
    } finally {
      set({ loading: false });
    }
  },

  // Special operations
  grantGuestAccess: async (grantData: any) => {
    set({ loading: true });
    try {
      const res = await apiClient.post('/guest/grant', grantData);
      return res.data;
    } catch (err: any) {
      console.error('Failed to grant guest access:', err);
      throw err;
    } finally {
      set({ loading: false });
    }
  },

  requestGuestAccess: async (requestData: any) => {
    set({ loading: true });
    try {
      const res = await apiClient.post('/guest/request', requestData);
      return res.data;
    } catch (err: any) {
      console.error('Failed to request guest access:', err);
      throw err;
    } finally {
      set({ loading: false });
    }
  },

  getCustomerStats: async (username: string) => {
    set({ loading: true });
    try {
      const res = await apiClient.get(`/customer/stats/${username}`);
      return res.data;
    } catch (err: any) {
      console.error('Failed to get customer stats:', err);
      throw err;
    } finally {
      set({ loading: false });
    }
  },
}));
