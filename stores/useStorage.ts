import { create } from 'zustand';
import {
  NASVlan,
  NASRole,
  User,
  CustomerInfo,
  PackageInfo,
  Group,
  NAS,
  Voucher,
  Analytics,
  DepartmentStats,
  RadAcctSession
} from '@/types/interface-type';
import apiClient from '@/lib/apiClient';

type DataStore = {
  // Data arrays
  users: User[];
  customers: CustomerInfo[];
  packages: PackageInfo[];
  groups: Group[];
  nasDevices: NAS[];
  nasVlan: NASVlan[];
  nasRole: NASRole[];
  vouchers: Voucher[];
  analytics: Analytics[];
  departmentStats: DepartmentStats[];
  sessions: RadAcctSession[];

  // Loading states
  loading: boolean;
  loadingUsers: boolean;
  loadingCustomers: boolean;
  loadingPackages: boolean;
  loadingGroups: boolean;
  loadingNasDevices: boolean;
  loadingVouchers: boolean;
  loadingAnalytics: boolean;

  // Fetch status flags
  usersFetched: boolean;
  customersFetched: boolean;
  packagesFetched: boolean;
  groupsFetched: boolean;
  nasDevicesFetched: boolean;
  nasVlanFetched: boolean;
  nasRoleFetched: boolean;
  vouchersFetched: boolean;
  analyticsFetched: boolean;

  // Error states
  usersError: string | null;
  customersError: string | null;
  packagesError: string | null;
  groupsError: string | null;
  nasDevicesError: string | null;
  vouchersError: string | null;
  analyticsError: string | null;

  // Setter functions
  setUsers: (users: User[]) => void;
  setCustomers: (customers: CustomerInfo[]) => void;
  setPackages: (packages: PackageInfo[]) => void;
  setGroups: (groups: Group[]) => void;
  setNasDevices: (devices: NAS[]) => void;
  setNasVlan: (nasVlan: NASVlan[]) => void;
  setNasRole: (nasRole: NASRole[]) => void;
  setVouchers: (vouchers: Voucher[]) => void;
  setAnalytics: (analytics: Analytics[]) => void;
  setDepartmentStats: (stats: DepartmentStats[]) => void;
  setSessions: (sessions: RadAcctSession[]) => void;
  setLoading: (state: boolean) => void;

  // Fetch functions
  fetchUsers: () => Promise<void>;
  fetchCustomers: () => Promise<void>;
  fetchPackages: () => Promise<void>;
  fetchGroups: () => Promise<void>;
  fetchNasDevices: () => Promise<void>;
  fetchNasVlans: () => Promise<void>;
  fetchNasRoles: () => Promise<void>;
  fetchVouchers: () => Promise<void>;
  fetchAnalytics: (params?: any) => Promise<void>;
  fetchDepartmentStats: (params?: any) => Promise<void>;
  fetchSessions: (endpoint: string) => Promise<void>;

  // Utility functions
  clearErrors: () => void;
  resetFetchFlags: () => void;
};

export const useDataStore = create<DataStore>((set, get) => ({
  // Initial data arrays
  users: [],
  customers: [],
  packages: [],
  groups: [],
  nasDevices: [],
  nasVlan: [],
  nasRole: [],
  vouchers: [],
  analytics: [],
  departmentStats: [],
  sessions: [],

  // Initial loading states
  loading: false,
  loadingUsers: false,
  loadingCustomers: false,
  loadingPackages: false,
  loadingGroups: false,
  loadingNasDevices: false,
  loadingVouchers: false,
  loadingAnalytics: false,

  // Initial fetch status flags
  usersFetched: false,
  customersFetched: false,
  packagesFetched: false,
  groupsFetched: false,
  nasDevicesFetched: false,
  nasVlanFetched: false,
  nasRoleFetched: false,
  vouchersFetched: false,
  analyticsFetched: false,

  // Initial error states
  usersError: null,
  customersError: null,
  packagesError: null,
  groupsError: null,
  nasDevicesError: null,
  vouchersError: null,
  analyticsError: null,

  // Setter functions
  setUsers: (users) => set({ users }),
  setCustomers: (customers) => set({ customers }),
  setPackages: (packages) => set({ packages }),
  setGroups: (groups) => set({ groups }),
  setNasDevices: (devices) => set({ nasDevices: devices }),
  setNasVlan: (nasVlan) => set({ nasVlan }),
  setNasRole: (nasRole) => set({ nasRole }),
  setVouchers: (vouchers) => set({ vouchers }),
  setAnalytics: (analytics) => set({ analytics }),
  setDepartmentStats: (stats) => set({ departmentStats: stats }),
  setSessions: (sessions) => set({ sessions }),
  setLoading: (loading) => set({ loading }),

  // Fetch functions
  fetchUsers: async () => {
    if (get().usersFetched) return;
    set({ loadingUsers: true, usersError: null });
    try {
      const res = await apiClient.get('/user');
      const data = res?.data || [];
      set({ users: data, usersFetched: true });
    } catch (err: any) {
      console.error('Failed to fetch users:', err);
      set({ usersError: err.message || 'Failed to fetch users', usersFetched: true });
    } finally {
      set({ loadingUsers: false });
    }
  },

  fetchCustomers: async () => {
    if (get().customersFetched) return;
    set({ loadingCustomers: true, customersError: null });
    try {
      const res = await apiClient.get('/customer');
      const data = res?.data || [];
      set({ customers: data, customersFetched: true });
    } catch (err: any) {
      console.error('Failed to fetch customers:', err);
      set({ customersError: err.message || 'Failed to fetch customers', customersFetched: true });
    } finally {
      set({ loadingCustomers: false });
    }
  },

  fetchPackages: async () => {
    if (get().packagesFetched) return;
    set({ loadingPackages: true, packagesError: null });
    try {
      const res = await apiClient.get('/package');
      const data = res?.data || [];
      set({ packages: data, packagesFetched: true });
    } catch (err: any) {
      console.error('Failed to fetch packages:', err);
      set({ packagesError: err.message || 'Failed to fetch packages', packagesFetched: true });
    } finally {
      set({ loadingPackages: false });
    }
  },

  fetchGroups: async () => {
    if (get().groupsFetched) return;
    set({ loadingGroups: true, groupsError: null });
    try {
      const res = await apiClient.get('/groups');
      const data = res?.data || [];
      set({ groups: data, groupsFetched: true });
    } catch (err: any) {
      console.error('Failed to fetch groups:', err);
      set({ groupsError: err.message || 'Failed to fetch groups', groupsFetched: true });
    } finally {
      set({ loadingGroups: false });
    }
  },

  fetchNasDevices: async () => {
    if (get().nasDevicesFetched) return;
    set({ loadingNasDevices: true, nasDevicesError: null });
    try {
      const res = await apiClient.get('/nas');
      const data = res?.data || [];
      set({ nasDevices: data, nasDevicesFetched: true });
    } catch (err: any) {
      console.error('Failed to fetch NAS devices:', err);
      set({ nasDevicesError: err.message || 'Failed to fetch NAS devices', nasDevicesFetched: true });
    } finally {
      set({ loadingNasDevices: false });
    }
  },

  fetchNasVlans: async () => {
    if (get().nasVlanFetched) return;
    set({ loading: true });
    try {
      const res = await apiClient.get('/nas/vlan');
      const data = res?.data || [];
      set({ nasVlan: data, nasVlanFetched: true });
    } catch (err: any) {
      console.error('Failed to fetch NAS VLANs:', err);
      set({ nasVlanFetched: true });
    } finally {
      set({ loading: false });
    }
  },

  fetchNasRoles: async () => {
    if (get().nasRoleFetched) return;
    set({ loading: true });
    try {
      const res = await apiClient.get('/nas/role');
      const data = res?.data || [];
      set({ nasRole: data, nasRoleFetched: true });
    } catch (err: any) {
      console.error('Failed to fetch NAS Roles:', err);
      set({ nasRoleFetched: true });
    } finally {
      set({ loading: false });
    }
  },

  fetchVouchers: async () => {
    if (get().vouchersFetched) return;
    set({ loadingVouchers: true, vouchersError: null });
    try {
      const res = await apiClient.get('/guest/voucher/?type=guest');
      const data = res?.data?.data || [];
      set({ vouchers: data, vouchersFetched: true });
    } catch (err: any) {
      console.error('Failed to fetch vouchers:', err);
      set({ vouchersError: err.message || 'Failed to fetch vouchers', vouchersFetched: true });
    } finally {
      set({ loadingVouchers: false });
    }
  },

  fetchAnalytics: async (params = {}) => {
    set({ loadingAnalytics: true, analyticsError: null });
    try {
      const res = await apiClient.post('/analytics', params);
      const data = res?.data?.data || [];
      set({ analytics: data, analyticsFetched: true });
    } catch (err: any) {
      console.error('Failed to fetch analytics:', err);
      set({ analyticsError: err.message || 'Failed to fetch analytics' });
    } finally {
      set({ loadingAnalytics: false });
    }
  },

  fetchDepartmentStats: async (params = {}) => {
    set({ loadingAnalytics: true, analyticsError: null });
    try {
      const res = await apiClient.post('/department/stats', params);
      const data = res?.data || [];
      set({ departmentStats: data });
    } catch (err: any) {
      console.error('Failed to fetch department stats:', err);
      set({ analyticsError: err.message || 'Failed to fetch department stats' });
    } finally {
      set({ loadingAnalytics: false });
    }
  },

  fetchSessions: async (endpoint: string) => {
    set({ loading: true });
    try {
      const res = await apiClient.get(endpoint);
      const data = res?.data || [];
      set({ sessions: data });
    } catch (err: any) {
      console.error('Failed to fetch sessions:', err);
    } finally {
      set({ loading: false });
    }
  },

  // Utility functions
  clearErrors: () => set({
    usersError: null,
    customersError: null,
    packagesError: null,
    groupsError: null,
    nasDevicesError: null,
    vouchersError: null,
    analyticsError: null,
  }),

  resetFetchFlags: () => set({
    usersFetched: false,
    customersFetched: false,
    packagesFetched: false,
    groupsFetched: false,
    nasDevicesFetched: false,
    nasVlanFetched: false,
    nasRoleFetched: false,
    vouchersFetched: false,
    analyticsFetched: false,
  }),
}));
