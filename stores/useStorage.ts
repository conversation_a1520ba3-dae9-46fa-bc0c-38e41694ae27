import { create } from 'zustand';
import { NASVlan, NASRole } from '@/types/interface-type';
import apiClient from '@/lib/apiClient';

type DataStore = {
  users: any[];
  devices: any[];
  loading: boolean;
  nasVlan: NASVlan[];
  nasRole: NASRole[];
  nasVlanFetched: boolean;
  nasRoleFetched: boolean;
  setUsers: (users: any[]) => void;
  setDevices: (devices: any[]) => void;
  setNasVlan: (nasVlan: NASVlan[]) => void;
  setNasRole: (nasRole: NASRole[]) => void;
  setLoading: (state: boolean) => void;

  fetchNasVlans: () => Promise<void>;
  fetchNasRoles: () => Promise<void>;
};

export const useDataStore = create<DataStore>((set, get) => ({
  users: [],
  devices: [],
  nasVlan: [],
  nasRole: [],
  nasVlanFetched: false,
  nasRoleFetched: false,
  loading: false,

  setUsers: (users) => set({ users }),
  setDevices: (devices) => set({ devices }),
  setNasVlan: (nasVlan) => set({ nasVlan }),
  setNasRole: (nasRole) => set({ nasRole }),
  setLoading: (loading) => set({ loading }),

  fetchNasVlans: async () => {
    if (get().nasVlanFetched) return; // prevent refetch
    set({ loading: true });
    try {
      const res = await apiClient.get('/nas/vlan');
      const data = res?.data || [];
      set({ nasVlan: data, nasVlanFetched: true });
    } catch (err) {
      console.error('Failed to fetch NAS VLANs:', err);
      set({ nasVlanFetched: true }); // Mark as fetched even on error to prevent infinite loops
    } finally {
      set({ loading: false });
    }
  },

  fetchNasRoles: async () => {
    if (get().nasRoleFetched) return; // prevent refetch
    set({ loading: true });
    try {
      const res = await apiClient.get('/nas/role');
      const data = res?.data || [];
      set({ nasRole: data, nasRoleFetched: true });
    } catch (err) {
      console.error('Failed to fetch NAS Roles:', err);
      set({ nasRoleFetched: true }); // Mark as fetched even on error to prevent infinite loops
    } finally {
      set({ loading: false });
    }
  },
}));
