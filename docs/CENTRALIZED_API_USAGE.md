# Centralized API Operations Usage Guide

This document provides comprehensive guidance on using the centralized API operations in the `useStorage.ts` store.

## Overview

All API operations (GET, POST, PATCH, DELETE) are now centralized in the `useDataStore` Zustand store. This eliminates the need for scattered API calls throughout components and provides consistent state management, loading states, and error handling.

## Import and Basic Usage

```typescript
import { useDataStore } from '@/stores/useStorage';

// In your component
const MyComponent = () => {
  const {
    // Data arrays
    users, customers, packages, groups, nasDevices,
    // Loading states
    loadingUsers, loadingCustomers, loadingPackages,
    // Error states
    usersError, customersError, packagesError,
    // Fetch functions
    fetchUsers, fetchCustomers, fetchPackages,
    // CRUD operations
    createUser, updateUser, deleteUser,
    createCustomer, updateCustomer, deleteCustomer,
    // ... other operations
  } = useDataStore();

  // Component logic here
};
```

## Available Data Entities

- **Users** (`/user`)
- **Customers** (`/customer`)
- **External Customers** (`/ecustomer`)
- **Packages/Departments** (`/package`)
- **Groups** (`/groups`, `/group`)
- **NAS Devices** (`/nas`)
- **NAS VLANs** (`/nas/vlan`)
- **NAS Roles** (`/nas/role`)
- **Vouchers** (`/guest/voucher`)
- **Analytics** (`/analytics`)
- **Department Stats** (`/department/stats`)
- **Sessions** (various endpoints)
- **Post Auth Data** (`/auth/postauth/`)

## CRUD Operations

### CREATE Operations (POST)

```typescript
// Create a new user
const handleCreateUser = async (userData) => {
  try {
    const result = await createUser(userData);
    toast.success('User created successfully');
    // Data is automatically refreshed in the store
  } catch (error) {
    toast.error(error.message || 'Failed to create user');
  }
};

// Create a new customer
const handleCreateCustomer = async (customerData) => {
  try {
    const result = await createCustomer(customerData);
    toast.success('Customer created successfully');
  } catch (error) {
    toast.error(error.message || 'Failed to create customer');
  }
};

// Create external customer
const handleCreateExternalCustomer = async (customerData) => {
  try {
    const result = await createExternalCustomer(customerData);
    toast.success('External customer created successfully');
  } catch (error) {
    toast.error(error.message || 'Failed to create external customer');
  }
};

// Create package/department
const handleCreatePackage = async (packageData) => {
  try {
    const result = await createPackage(packageData);
    toast.success('Department created successfully');
  } catch (error) {
    toast.error(error.message || 'Failed to create department');
  }
};

// Create group
const handleCreateGroup = async (groupData) => {
  try {
    const result = await createGroup(groupData);
    toast.success('Group created successfully');
  } catch (error) {
    toast.error(error.message || 'Failed to create group');
  }
};

// Create NAS device
const handleCreateNasDevice = async (nasData) => {
  try {
    const result = await createNasDevice(nasData);
    toast.success('Device created successfully');
  } catch (error) {
    toast.error(error.message || 'Failed to create device');
  }
};

// Create NAS role
const handleCreateNasRole = async (roleData) => {
  try {
    const result = await createNasRole(roleData);
    toast.success('Role created successfully');
  } catch (error) {
    toast.error(error.message || 'Failed to create role');
  }
};

// Create NAS VLAN
const handleCreateNasVlan = async (vlanData) => {
  try {
    const result = await createNasVlan(vlanData);
    toast.success('VLAN created successfully');
  } catch (error) {
    toast.error(error.message || 'Failed to create VLAN');
  }
};

// Create voucher
const handleCreateVoucher = async (voucherData) => {
  try {
    const result = await createVoucher(voucherData);
    toast.success('Voucher created successfully');
  } catch (error) {
    toast.error(error.message || 'Failed to create voucher');
  }
};
```

### READ Operations (GET)

```typescript
// Fetch data (automatically prevents duplicate calls)
useEffect(() => {
  fetchUsers();
  fetchCustomers();
  fetchPackages();
  fetchGroups();
  fetchNasDevices();
  fetchVouchers();
}, []);

// Fetch analytics with parameters
const handleFetchAnalytics = async () => {
  const params = {
    created_from: startDate,
    created_to: endDate
  };
  await fetchAnalytics(params);
};

// Fetch department stats
const handleFetchDepartmentStats = async () => {
  const params = {
    created_from: startDate,
    created_to: endDate
  };
  await fetchDepartmentStats(params);
};

// Fetch sessions with custom endpoint
const handleFetchSessions = async () => {
  await fetchSessions('/radacct/sessions');
};

// Fetch external customers
const handleFetchExternalCustomers = async () => {
  await fetchExternalCustomers();
};

// Fetch post auth data
const handleFetchPostAuthData = async () => {
  await fetchPostAuthData();
};
```

### UPDATE Operations (PATCH)

```typescript
// Update user
const handleUpdateUser = async (id, userData) => {
  try {
    const result = await updateUser(id, userData);
    toast.success('User updated successfully');
  } catch (error) {
    toast.error(error.message || 'Failed to update user');
  }
};

// Update customer
const handleUpdateCustomer = async (id, customerData) => {
  try {
    const result = await updateCustomer(id, customerData);
    toast.success('Customer updated successfully');
  } catch (error) {
    toast.error(error.message || 'Failed to update customer');
  }
};

// Update package/department
const handleUpdatePackage = async (id, packageData) => {
  try {
    const result = await updatePackage(id, packageData);
    toast.success('Department updated successfully');
  } catch (error) {
    toast.error(error.message || 'Failed to update department');
  }
};

// Update group (uses groupname instead of id)
const handleUpdateGroup = async (groupname, groupData) => {
  try {
    const result = await updateGroup(groupname, groupData);
    toast.success('Group updated successfully');
  } catch (error) {
    toast.error(error.message || 'Failed to update group');
  }
};

// Update NAS device
const handleUpdateNasDevice = async (id, nasData) => {
  try {
    const result = await updateNasDevice(id, nasData);
    toast.success('Device updated successfully');
  } catch (error) {
    toast.error(error.message || 'Failed to update device');
  }
};

// Update NAS role
const handleUpdateNasRole = async (id, roleData) => {
  try {
    const result = await updateNasRole(id, roleData);
    toast.success('Role updated successfully');
  } catch (error) {
    toast.error(error.message || 'Failed to update role');
  }
};

// Update NAS VLAN
const handleUpdateNasVlan = async (id, vlanData) => {
  try {
    const result = await updateNasVlan(id, vlanData);
    toast.success('VLAN updated successfully');
  } catch (error) {
    toast.error(error.message || 'Failed to update VLAN');
  }
};
```

### DELETE Operations

```typescript
// Delete user
const handleDeleteUser = async (id) => {
  try {
    await deleteUser(id);
    toast.success('User deleted successfully');
  } catch (error) {
    toast.error(error.message || 'Failed to delete user');
  }
};

// Delete customer
const handleDeleteCustomer = async (id) => {
  try {
    await deleteCustomer(id);
    toast.success('Customer deleted successfully');
  } catch (error) {
    toast.error(error.message || 'Failed to delete customer');
  }
};

// Delete package/department
const handleDeletePackage = async (id) => {
  try {
    await deletePackage(id);
    toast.success('Department deleted successfully');
  } catch (error) {
    toast.error(error.message || 'Failed to delete department');
  }
};

// Delete group (uses groupname instead of id)
const handleDeleteGroup = async (groupname) => {
  try {
    await deleteGroup(groupname);
    toast.success('Group deleted successfully');
  } catch (error) {
    toast.error(error.message || 'Failed to delete group');
  }
};

// Delete NAS device
const handleDeleteNasDevice = async (id) => {
  try {
    await deleteNasDevice(id);
    toast.success('Device deleted successfully');
  } catch (error) {
    toast.error(error.message || 'Failed to delete device');
  }
};

// Delete NAS role
const handleDeleteNasRole = async (id) => {
  try {
    await deleteNasRole(id);
    toast.success('Role deleted successfully');
  } catch (error) {
    toast.error(error.message || 'Failed to delete role');
  }
};

// Delete NAS VLAN
const handleDeleteNasVlan = async (id) => {
  try {
    await deleteNasVlan(id);
    toast.success('VLAN deleted successfully');
  } catch (error) {
    toast.error(error.message || 'Failed to delete VLAN');
  }
};
```

## Special Operations

### Guest Access Operations

```typescript
// Grant guest access (no authentication required)
const handleGrantGuestAccess = async (grantData) => {
  try {
    const result = await grantGuestAccess(grantData);
    toast.success('Guest access granted successfully');
    return result;
  } catch (error) {
    toast.error(error.message || 'Failed to grant guest access');
  }
};

// Request guest access (no authentication required)
const handleRequestGuestAccess = async (requestData) => {
  try {
    const result = await requestGuestAccess(requestData);
    toast.success('Guest access requested successfully');
    return result;
  } catch (error) {
    toast.error(error.message || 'Failed to request guest access');
  }
};

// Get customer statistics
const handleGetCustomerStats = async (username) => {
  try {
    const stats = await getCustomerStats(username);
    return stats;
  } catch (error) {
    toast.error(error.message || 'Failed to get customer stats');
  }
};
```

## Loading States and Error Handling

### Using Loading States

```typescript
const MyComponent = () => {
  const {
    users,
    loadingUsers,
    usersError,
    fetchUsers
  } = useDataStore();

  useEffect(() => {
    fetchUsers();
  }, []);

  if (loadingUsers) {
    return <div>Loading users...</div>;
  }

  if (usersError) {
    return <div>Error: {usersError}</div>;
  }

  return (
    <div>
      {users.map(user => (
        <div key={user.id}>{user.username}</div>
      ))}
    </div>
  );
};
```

### Error Handling Best Practices

```typescript
// Clear errors when needed
const { clearErrors } = useDataStore();

const handleClearErrors = () => {
  clearErrors();
};

// Reset fetch flags to force refetch
const { resetFetchFlags } = useDataStore();

const handleForceRefresh = () => {
  resetFetchFlags();
  fetchUsers();
  fetchCustomers();
};
```

## Migration from Old Pattern

### Before (Old Pattern)
```typescript
// OLD: Direct API calls in components
const [users, setUsers] = useState([]);
const [loading, setLoading] = useState(false);
const [error, setError] = useState(null);

useEffect(() => {
  const fetchUsers = async () => {
    setLoading(true);
    try {
      const response = await apiClient.get('/user');
      setUsers(response.data || []);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  fetchUsers();
}, []);

const handleCreateUser = async (userData) => {
  try {
    await apiClient.post('/user', userData);
    // Manual refresh
    fetchUsers();
  } catch (error) {
    toast.error(error.message);
  }
};
```

### After (New Centralized Pattern)
```typescript
// NEW: Centralized API operations
const {
  users,
  loadingUsers,
  usersError,
  fetchUsers,
  createUser
} = useDataStore();

useEffect(() => {
  fetchUsers(); // Automatic duplicate prevention
}, []);

const handleCreateUser = async (userData) => {
  try {
    await createUser(userData); // Automatic refresh
    toast.success('User created successfully');
  } catch (error) {
    toast.error(error.message);
  }
};
```

## Benefits

1. **Centralized State Management**: All data is managed in one place
2. **Automatic Refresh**: CRUD operations automatically refresh related data
3. **Duplicate Prevention**: Fetch functions prevent unnecessary API calls
4. **Consistent Loading States**: Standardized loading and error handling
5. **Type Safety**: Full TypeScript support with proper interfaces
6. **Error Handling**: Centralized error state management
7. **Performance**: Optimized with fetch flags and state management
8. **Maintainability**: Single source of truth for all API operations

## Important Notes

- All CRUD operations automatically refresh the related data after completion
- Fetch functions use flags to prevent duplicate API calls
- Loading states are automatically managed for each operation
- Error states are centralized and can be cleared globally
- The store uses Zustand for optimal performance and minimal re-renders
- All operations return promises and can be awaited
- Toast notifications should be handled in components, not in the store
