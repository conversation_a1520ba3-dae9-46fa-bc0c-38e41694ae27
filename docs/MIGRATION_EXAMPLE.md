# Migration Example: Refactoring Components to Use Centralized API

This document shows a complete example of how to migrate a component from using direct API calls to the centralized API operations.

## Before: Component with Direct API Calls

```typescript
// OLD: app/app/settings/users/page.tsx (Before Migration)
"use client";

import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import apiClient from "@/lib/apiClient";
import { User } from "@/types/interface-type";
import { toast } from "sonner";
import UserForm from "@/components/settings/users/user-form";
import DeleteConfirm from "@/components/delete-dailog";

export default function UsersPage() {
  // Local state management (OLD PATTERN)
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isAddOpen, setIsAddOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isRefreshed, setIsRefreshed] = useState(false);

  // Manual API calls (OLD PATTERN)
  useEffect(() => {
    const fetchUsers = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await apiClient.get('/user');
        setUsers(response.data || []);
      } catch (err: any) {
        console.error('Failed to fetch users:', err);
        setError(err.message || 'Failed to fetch users');
        toast.error(err.message || 'Failed to fetch users');
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, [isRefreshed]);

  // Manual create operation (OLD PATTERN)
  const handleAddSubmit = async (newUser: any) => {
    console.log("newUser payload being sent:", newUser);

    try {
      const res = await apiClient.post("/user", newUser);
      console.log(res?.message);
      toast.success(res?.message || "User created successfully");
      setIsAddOpen(false);
      setIsRefreshed((prev) => !prev); // Manual refresh trigger
    } catch (error: any) {
      console.log("Failed to add user:", error);
      const errorMsg =
        error?.response?.data?.errors?.[0]?.msg || "Failed to add user";
      toast.error(errorMsg);
    }
  };

  // Manual update operation (OLD PATTERN)
  const handleEditSubmit = async (updatedUser: any) => {
    const { id, ...payload } = updatedUser;
    try {
      const response = await apiClient.patch(`/user/${id}`, payload);
      toast.success("User updated successfully");
      setIsEditOpen(false);
      setIsRefreshed((prev) => !prev); // Manual refresh trigger
    } catch (error: any) {
      console.error("Failed to update user:", error);
      toast.error(error.message || "Failed to update user");
    }
  };

  // Manual delete operation (OLD PATTERN)
  const handleDelete = async (id: number) => {
    try {
      await apiClient.delete(`/user/${id}`);
      toast.success("User deleted successfully");
      setIsRefreshed((prev) => !prev); // Manual refresh trigger
    } catch (error: any) {
      console.error("Failed to delete user:", error);
      toast.error(error.message || "Failed to delete user");
    }
  };

  // Manual loading and error handling (OLD PATTERN)
  if (loading) {
    return <div>Loading users...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">User Management</h1>
        <Button onClick={() => setIsAddOpen(true)}>
          Add User
        </Button>
      </div>

      <div className="grid gap-4">
        {users.map((user) => (
          <div key={user.id} className="border p-4 rounded">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="font-semibold">{user.username}</h3>
                <p className="text-gray-600">{user.email}</p>
              </div>
              <div className="space-x-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setSelectedUser(user);
                    setIsEditOpen(true);
                  }}
                >
                  Edit
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => handleDelete(user.id)}
                >
                  Delete
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {isAddOpen && (
        <UserForm
          onSubmit={handleAddSubmit}
          onCancel={() => setIsAddOpen(false)}
        />
      )}

      {isEditOpen && selectedUser && (
        <UserForm
          user={selectedUser}
          onSubmit={handleEditSubmit}
          onCancel={() => setIsEditOpen(false)}
        />
      )}
    </div>
  );
}
```

## After: Component Using Centralized API Operations

```typescript
// NEW: app/app/settings/users/page.tsx (After Migration)
"use client";

import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { User } from "@/types/interface-type";
import { toast } from "sonner";
import UserForm from "@/components/settings/users/user-form";
import DeleteConfirm from "@/components/delete-dailog";
import { useDataStore } from "@/stores/useStorage"; // NEW: Import centralized store

export default function UsersPage() {
  // NEW: Use centralized store instead of local state
  const {
    users,
    loadingUsers,
    usersError,
    fetchUsers,
    createUser,
    updateUser,
    deleteUser,
  } = useDataStore();

  // Local UI state only (not data state)
  const [isAddOpen, setIsAddOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  // NEW: Simple fetch call - no manual loading/error handling needed
  useEffect(() => {
    fetchUsers(); // Automatic duplicate prevention
  }, []);

  // NEW: Simplified create operation
  const handleAddSubmit = async (newUser: any) => {
    try {
      await createUser(newUser); // Automatic refresh after creation
      toast.success("User created successfully");
      setIsAddOpen(false);
    } catch (error: any) {
      // Error handling is centralized, just show user feedback
      const errorMsg =
        error?.response?.data?.errors?.[0]?.msg || "Failed to add user";
      toast.error(errorMsg);
    }
  };

  // NEW: Simplified update operation
  const handleEditSubmit = async (updatedUser: any) => {
    const { id, ...payload } = updatedUser;
    try {
      await updateUser(id, payload); // Automatic refresh after update
      toast.success("User updated successfully");
      setIsEditOpen(false);
    } catch (error: any) {
      toast.error(error.message || "Failed to update user");
    }
  };

  // NEW: Simplified delete operation
  const handleDelete = async (id: number) => {
    try {
      await deleteUser(id); // Automatic refresh after deletion
      toast.success("User deleted successfully");
    } catch (error: any) {
      toast.error(error.message || "Failed to delete user");
    }
  };

  // NEW: Centralized loading and error handling
  if (loadingUsers) {
    return <div>Loading users...</div>;
  }

  if (usersError) {
    return <div>Error: {usersError}</div>;
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">User Management</h1>
        <Button onClick={() => setIsAddOpen(true)}>
          Add User
        </Button>
      </div>

      <div className="grid gap-4">
        {users.map((user) => (
          <div key={user.id} className="border p-4 rounded">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="font-semibold">{user.username}</h3>
                <p className="text-gray-600">{user.email}</p>
              </div>
              <div className="space-x-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setSelectedUser(user);
                    setIsEditOpen(true);
                  }}
                >
                  Edit
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => handleDelete(user.id)}
                >
                  Delete
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {isAddOpen && (
        <UserForm
          onSubmit={handleAddSubmit}
          onCancel={() => setIsAddOpen(false)}
        />
      )}

      {isEditOpen && selectedUser && (
        <UserForm
          user={selectedUser}
          onSubmit={handleEditSubmit}
          onCancel={() => setIsEditOpen(false)}
        />
      )}
    </div>
  );
}
```

## Key Changes Summary

### Removed (OLD)
- ❌ Local state for data (`useState` for users, loading, error)
- ❌ Manual API calls with `apiClient.get/post/patch/delete`
- ❌ Manual loading state management
- ❌ Manual error state management
- ❌ Manual refresh triggers (`setIsRefreshed`)
- ❌ Duplicate API call prevention logic
- ❌ Manual data synchronization

### Added (NEW)
- ✅ Centralized store import (`useDataStore`)
- ✅ Destructured store properties (data, loading, error, operations)
- ✅ Simplified CRUD operations with automatic refresh
- ✅ Centralized loading and error states
- ✅ Automatic duplicate prevention
- ✅ Consistent error handling patterns

## Benefits of Migration

1. **Reduced Code**: ~50% less code in components
2. **Consistency**: Standardized patterns across all components
3. **Maintainability**: Single source of truth for API operations
4. **Performance**: Optimized state management and duplicate prevention
5. **Error Handling**: Centralized and consistent error management
6. **Type Safety**: Full TypeScript support with proper interfaces
7. **Automatic Refresh**: No manual refresh triggers needed
8. **State Synchronization**: Automatic data synchronization across components

## Migration Checklist

- [ ] Import `useDataStore` from `@/stores/useStorage`
- [ ] Replace local data state with store properties
- [ ] Replace manual API calls with store operations
- [ ] Remove manual loading/error state management
- [ ] Remove manual refresh triggers
- [ ] Update error handling to use centralized states
- [ ] Test all CRUD operations
- [ ] Verify automatic refresh functionality
- [ ] Check loading states and error handling
- [ ] Remove unused imports and state variables
