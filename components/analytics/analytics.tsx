"use client";
import React, { useState, useEffect, useMemo } from "react";
import { toast } from "sonner";
import apiClient from "@/lib/apiClient";
import { Button } from "../ui/button";
import { useDataStore } from "@/stores/useStorage";
import {
  ChevronLeft,
  ChevronRight,
  Sessions,
  Users,
  Network,
} from "@/components/icons/list";
import { useSearchParams, useRouter } from "next/navigation";
import { DepartmentStats, PackageInfo } from "@/types/interface-type";
import {
  <PERSON><PERSON><PERSON>,
  LineChart,
  Line,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from "recharts";

// Simplified interfaces
interface BarChartData {
  name: string;
  download: number;
  upload: number;
}

export interface LineChartData {
  timestamp: string;
  totalUsage: number;
  count: number;
}

export interface DepartmentOverviewData {
  departmentName: string;
  staffUsers: number;
  guestUsers: number;
  staffUsage: number; // in GB
  guestUsage: number; // in GB
  totalUsage: number; // in GB
  totalUsers: number;
}

interface ActiveStat extends DepartmentStats {
  inputMbps: number;
  outputMbps: number;
  count: number;
  recordId: string;
  created_at: string;
}

// Utility functions
const getDefaultDates = () => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  const today = new Date();
  return {
    start: yesterday.toISOString().split("T")[0],
    end: today.toISOString().split("T")[0],
  };
};

// Utility functions for filling missing time periods
const generateDateRange = (
  startDateStr: string,
  endDateStr: string
): string[] => {
  const start = new Date(startDateStr);
  const end = new Date(endDateStr);
  const dates: string[] = [];

  while (start <= end) {
    dates.push(new Date(start).toISOString().split("T")[0]); // "YYYY-MM-DD"
    start.setDate(start.getDate() + 1);
  }

  return dates;
};

const generateHourlyRange = (
  startDateStr: string,
  endDateStr: string
): string[] => {
  const start = new Date(`${startDateStr} 00:00:00`);
  const end = new Date(`${endDateStr} 23:59:59`);
  const hours: string[] = [];

  const current = new Date(start);
  while (current <= end) {
    hours.push(current.toISOString());
    current.setHours(current.getHours() + 1);
  }

  return hours;
};

const fillMissingHours = (
  chartData: LineChartData[],
  startDate: string,
  endDate: string
): LineChartData[] => {
  const hourlyRange = generateHourlyRange(startDate, endDate);

  return hourlyRange.map((hour) => {
    const existingData = chartData.find((d) => d.timestamp === hour);
    return (
      existingData || {
        timestamp: hour,
        totalUsage: 0,
        count: 0,
      }
    );
  });
};

const fillMissingDates = (
  chartData: LineChartData[],
  startDate: string,
  endDate: string
): LineChartData[] => {
  return generateDateRange(startDate, endDate).map((day) => {
    const dayEntries = chartData.filter((d) => d.timestamp.startsWith(day));
    const maxCount = dayEntries.length
      ? Math.max(...dayEntries.map((d) => d.count))
      : 0;
    const totalUsage = dayEntries.reduce((sum, d) => sum + d.totalUsage, 0);

    return {
      timestamp: day,
      totalUsage: parseFloat(totalUsage.toFixed(2)),
      count: maxCount,
    };
  });
};

// Simplified chart components
const AnalyticsBarChart: React.FC<{ data: BarChartData[] }> = ({ data }) => {
  const filteredChartData = (data || [])
    .map((item: BarChartData) => ({
      name: item.name,
      total: (item.download || 0) + (item.upload || 0),
    }))
    .filter((item: { name: string; total: number }) => item.total > 0);

  const chartData = data.length > 0 ? data : [{ name: "No data", total: 0 }];

  return (
    <ResponsiveContainer width="100%" height="100%">
      <section>
        <header className="text-center">
          Top Users by Bandwidth Usage (GB)
        </header>
      </section>
      <BarChart
        data={chartData}
        layout="vertical"
        margin={{ top: 10, right: 20, left: 20, bottom: 10 }}
        barCategoryGap={25}
      >
        <CartesianGrid strokeDasharray="3 3" />
        <YAxis dataKey="group" type="category" hide />
        <XAxis type="number" />
        <Tooltip />
        <Legend
          verticalAlign="top"
          align="right"
          layout="horizontal"
          wrapperStyle={{ top: 0, right: 0 }}
        />
        <Bar
          dataKey="total"
          fill="#0ea516ff"
          radius={[0, 30, 30, 0]}
          label={({ x, y, height, group }) => {
            if (!group) return null;
            return (
              <text
                x={x + 15}
                y={y + height / 2.5}
                fill="black"
                fontSize={12}
                alignmentBaseline="middle"
                textAnchor="start"
              >
                {group}
              </text>
            );
          }}
        />
      </BarChart>
    </ResponsiveContainer>
  );
};

const DepartmentBarChart: React.FC<{ data: BarChartData[] }> = ({ data }) => {
  const filteredChartData = (data || []).filter(
    (item: BarChartData) => item.download > 0 || item.upload > 0
  );

  const chartData = filteredChartData.length
    ? filteredChartData
    : [{ name: "No data", download: 0, upload: 0 }];

  return (
    <ResponsiveContainer width="100%" height="100%">
      <section>
        <header className="text-center">
          Top Departments by Bandwidth Usage (GB)
        </header>
      </section>
      <BarChart
        data={chartData}
        layout="horizontal"
        margin={{ top: 1, right: 5, left: 5, bottom: 10 }}
      >
        <CartesianGrid strokeDasharray="0 0" />
        <XAxis dataKey="name" type="category" />
        <YAxis type="number" />
        <Tooltip />
        <Legend
          verticalAlign="top"
          align="right"
          layout="horizontal"
          wrapperStyle={{ top: 0, right: 0 }}
        />
        <Bar
          dataKey="download"
          fill="#0d9614ff"
        // radius={[0, 30, 30, 0]}
        />
        <Bar
          dataKey="upload"
          fill="#dc2626"
          position="inside"
        // radius={[0, 30, 30, 0]}
        />
      </BarChart>
    </ResponsiveContainer>
  );
};

const COLORS = [
  "#4ade80",
  "#22d3ee",
  "#facc15",
  "#f97316",
  "#ec4899",
  "#94a3b8",
];

// Staff Users Pie Chart - Top 5 Staff Users
const StaffPieChart: React.FC<{ data: DepartmentStats[]; customers: any[] }> = ({ data, customers }) => {
  // Check if data is available
  if (!data || data.length === 0) {
    return (
      <ResponsiveContainer width="100%" height="100%">
        <div className="flex items-center justify-center h-full">
          <div className="text-center text-gray-500">
            <h3 className="text-lg font-semibold mb-2">Top 5 Staff Users by Usage (GB)</h3>
            <p>No analytics data available</p>
          </div>
        </div>
      </ResponsiveContainer>
    );
  }

  // Get all customer usernames to identify staff (empty set if no customers loaded yet)
  const customerUsernames = new Set(customers && customers.length > 0 ? customers.map((customer: any) => customer.username) : []);

  // Filter for staff users only
  const staffData = data.filter((stat: DepartmentStats) => customerUsernames.has(stat.username));

  // If no staff data found, show appropriate message
  if (staffData.length === 0) {
    const message = customers && customers.length > 0 ? "No staff usage found" : "Loading staff data...";
    return (
      <ResponsiveContainer width="100%" height="100%">
        <div className="flex items-center justify-center h-full">
          <div className="text-center text-gray-500">
            <h3 className="text-lg font-semibold mb-2">Top 5 Staff Users by Usage (GB)</h3>
            <p>{message}</p>
          </div>
        </div>
      </ResponsiveContainer>
    );
  }

  // Aggregate staff usage by username
  const staffUsageMap = new Map<string, number>();
  staffData.forEach((stat: DepartmentStats) => {
    const username = stat.username;
    const usage = (Number(stat.acctinputoctets || 0) + Number(stat.acctoutputoctets || 0)) / (1024 * 1024 * 1024);
    staffUsageMap.set(username, (staffUsageMap.get(username) || 0) + usage);
  });

  const sorted = [...totalData].sort((a, b) => b.total - a.total);
  const top5 = sorted.slice(0, 5);
  // const othersTotal = sorted
  //   .slice(5)
  //   .reduce((sum, item) => sum + item.total, 0);
  const staffUsageArray = Array.from(staffUsageMap.entries())
    .map(([name, total]) => ({ name, total: parseFloat(total.toFixed(2)) }))
    .filter((item) => item.total > 0)
    .sort((a, b) => b.total - a.total);

  const top5Staff = staffUsageArray.slice(0, 5);
  const othersTotal = staffUsageArray.slice(5).reduce((sum, item) => sum + item.total, 0);

  if (othersTotal > 0) {
    top5Staff.push({ name: "Others", total: parseFloat(othersTotal.toFixed(2)) });
  }

  const chartData = top5Staff.length ? top5Staff : [{ name: "No staff data", total: 0 }];

  return (
    <ResponsiveContainer width="100%" height="100%">
      <section><header className="text-center">Top 5 Staff Users by Usage (GB)</header></section>
      <PieChart>
        <Pie
          data={chartData}
          dataKey="total"
          nameKey="name"
          outerRadius={100}
          label={({ name, percent }) => `${name} (${(percent * 100).toFixed(1)}%)`}
        >
          {chartData.map((_entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Pie>
        <Tooltip formatter={(value: any) => [`${value} GB`, 'Usage']} />
        <Legend verticalAlign="top" align="center" layout="horizontal" wrapperStyle={{ top: 0, right: 0 }} />
      </PieChart>
    </ResponsiveContainer>
  );
};

// Guest Users Pie Chart - Top 5 Guest Users
const GuestPieChart: React.FC<{ data: DepartmentStats[]; customers: any[] }> = ({ data, customers }) => {
  // Check if data is available
  if (!data || data.length === 0) {
    return (
      <ResponsiveContainer width="100%" height="100%">
        <div className="flex items-center justify-center h-full">
          <div className="text-center text-gray-500">
            <h3 className="text-lg font-semibold mb-2">Top 5 Guest Users by Usage (GB)</h3>
            <p>No guest data available</p>
          </div>
        </div>
      </ResponsiveContainer>
    );
  }

  // Get all customer usernames to identify staff (empty set if no customers)
  const customerUsernames = new Set(customers && customers.length > 0 ? customers.map((customer: any) => customer.username) : []);

  // Filter for guest users only (users not in customer list)
  const guestData = data.filter((stat: DepartmentStats) => !customerUsernames.has(stat.username));

  // If no guest data found, show appropriate message
  if (guestData.length === 0) {
    return (
      <ResponsiveContainer width="100%" height="100%">
        <div className="flex items-center justify-center h-full">
          <div className="text-center text-gray-500">
            <h3 className="text-lg font-semibold mb-2">Top 5 Guest Users by Usage (GB)</h3>
            <p>No guest usage found</p>
          </div>
        </div>
      </ResponsiveContainer>
    );
  }

  // Aggregate guest usage by username
  const guestUsageMap = new Map<string, number>();
  guestData.forEach((stat: DepartmentStats) => {
    const username = stat.username;
    const usage = (Number(stat.acctinputoctets || 0) + Number(stat.acctoutputoctets || 0)) / (1024 * 1024 * 1024);
    guestUsageMap.set(username, (guestUsageMap.get(username) || 0) + usage);
  });

  const guestUsageArray = Array.from(guestUsageMap.entries())
    .map(([name, total]) => ({ name, total: parseFloat(total.toFixed(2)) }))
    .filter((item) => item.total > 0)
    .sort((a, b) => b.total - a.total);

  const top5Guests = guestUsageArray.slice(0, 5);
  const othersTotal = guestUsageArray.slice(5).reduce((sum, item) => sum + item.total, 0);

  if (othersTotal > 0) {
    top5Guests.push({ name: "Others", total: parseFloat(othersTotal.toFixed(2)) });
  }

  const chartData = top5Guests.length ? top5Guests : [{ name: "No guest data", total: 0 }];

  return (
    <ResponsiveContainer width="100%" height="100%">
      <section><header className="text-center">Top 5 Guest Users by Usage (GB)</header></section>
      <PieChart>
        <Pie
          data={chartData}
          dataKey="total"
          nameKey="name"
          outerRadius={100}
          label={({ name, percent }) => `${name} (${(percent * 100).toFixed(1)}%)`}
        >
          {chartData.map((_entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Pie>
        <Tooltip formatter={(value: any) => [`${value} GB`, 'Usage']} />
        <Legend verticalAlign="top" align="center" layout="horizontal" wrapperStyle={{ top: 0, right: 0 }} />
      </PieChart>
    </ResponsiveContainer>
  );
};

const AnalyticsLineChart: React.FC<{
  data: LineChartData[];
  startDate: string;
}> = ({ data, startDate }) => {
  // Data is already filled with missing days from the processing function
  const chartData = data?.length
    ? data
    : [{ timestamp: startDate, totalUsage: 0, count: 0 }];

  return (
    <ResponsiveContainer width="100%" height="100%">
      <h1 className="text-center">Data Usage Over Time (MB)</h1>
      <LineChart
        data={chartData}
        margin={{ top: 1, right: 5, left: 5, bottom: 0 }}
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis
          dataKey="timestamp"
          type="category"
          interval="preserveStartEnd"
          tickFormatter={(v: any) => {
            try {
              const date = new Date(v);
              if (isNaN(date.getTime())) return "";

              // Check if this is hourly data (has time component) or daily data
              const isHourlyData =
                v.includes("T") && !v.endsWith("T00:00:00.000Z");

              if (isHourlyData) {
                // For hourly data, show date and hour
                return date.toLocaleDateString(undefined, {
                  month: "short",
                  day: "numeric",
                  // }) + " " + date.toLocaleTimeString(undefined, {
                  //   hour: "2-digit",
                  //   hour12: false
                });
              } else {
                // For daily data, show just the date
                return date.toLocaleDateString(undefined, {
                  day: "2-digit",
                  month: "short",
                });
              }
            } catch {
              return "";
            }
          }}
          angle={-45}
          textAnchor="end"
          height={80}
        />
        <YAxis domain={[0, "auto"]} allowDecimals={false} />
        <Tooltip />
        <Legend
          verticalAlign="top"
          align="right"
          layout="horizontal"
          wrapperStyle={{ top: 0, right: 0 }}
        />
        <Line
          type="monotone"
          dataKey="totalUsage"
          stroke="#e44d12ff"
          strokeWidth={2}
          dot={false}
          activeDot={{ r: 4 }}
        />
      </LineChart>
    </ResponsiveContainer>
  );
};

const AnalyticsLineChartforCount: React.FC<{
  data: LineChartData[];
  startDate: string;
  endDate: string;
}> = ({ data, startDate, endDate }) => {
  // Data is already filled with missing hours/days from the processing function
  const chartData = data?.length
    ? data
    : [{ timestamp: startDate, totalUsage: 0, count: 0 }];

  // Fill missing dates by aggregating hourly data to daily data
  const filledChartData = fillMissingDates(chartData, startDate, endDate);

  return (
    <ResponsiveContainer width="100%" height="100%">
      <h1 className="text-center">Active Users Count Over Time</h1>
      <LineChart
        data={filledChartData}
        margin={{ top: 1, right: 5, left: 5, bottom: 5 }}
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis
          dataKey="timestamp"
          type="category"
          interval="preserveStartEnd"
          tickFormatter={(v: any) => {
            try {
              const date = new Date(v);
              if (isNaN(date.getTime())) return "";
              return date.toLocaleDateString(undefined, {
                day: "2-digit",
                month: "short", // e.g., "Jul"
              });
            } catch {
              return "";
            }
          }}
          angle={-45}
          textAnchor="end"
          height={60}
        />

        <YAxis
          domain={[0, "auto"]} // Always starts at 0
          allowDecimals={false}
        />

        <Legend
          verticalAlign="top"
          align="right"
          layout="horizontal"
          wrapperStyle={{ top: 0, right: 0 }}
        />
        <Line
          type="step"
          dataKey="count"
          stroke="#3b82f6"
          strokeWidth={2}
          dot={false}
          activeDot={{ r: 4 }}
        />
      </LineChart>
    </ResponsiveContainer>
  );
};

// Department Overview Chart with Staff/Guest separation
const DepartmentOverviewChart: React.FC<{ data: DepartmentOverviewData[] }> = ({ data }) => {
  const chartData = data.length ? data : [{
    departmentName: "No data",
    staffUsers: 0,
    guestUsers: 0,
    staffUsage: 0,
    guestUsage: 0,
    totalUsage: 0,
    totalUsers: 0
  }];

  return (
    <ResponsiveContainer width="100%" height="100%">
      <section><header className="text-center">Department Usage: Staff vs Guest (GB)</header></section>
      <BarChart
        data={chartData}
        margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis
          dataKey="departmentName"
          // angle={-45}
          // textAnchor="end"
          height={80}
        />
        <YAxis />
        <Tooltip
          formatter={(value: any, name: string) => [
            `${parseFloat(value).toFixed(2)} GB`,
            name === 'staffUsage' ? 'Staff Usage' : 'Guest Usage'
          ]}
          labelFormatter={(label: any) => `Department: ${label}`}
        />
        <Legend verticalAlign="top" align="right" layout="horizontal" wrapperStyle={{ top: 0, right: 0 }} />
        <Bar
          dataKey="staffUsage"
          fill="#22c55e"
          name="Staff Usage"
        />
        <Bar
          dataKey="guestUsage"
          fill="#f59e0b"
          name="Guest Usage"
        />
      </BarChart>
    </ResponsiveContainer>
  );
};

// Department Overview Summary Table
const DepartmentOverviewTable: React.FC<{ data: DepartmentOverviewData[]; loading?: boolean }> = ({ data, loading = false }) => {
  console.log('DepartmentOverviewTable received data:', data, 'loading:', loading);

  if (loading) {
    return (
      <div className="text-center py-8 text-gray-500">
        <h3 className="text-lg font-semibold mb-4">Department Overview: Staff vs Guest</h3>
        <p>Loading department data...</p>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <h3 className="text-lg font-semibold mb-4">Department Overview: Staff vs Guest</h3>
        <p>No department data available. No usage found for the selected period.</p>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <h3 className="text-lg font-semibold mb-4 text-center">Department Overview: Staff vs Guest</h3>
      <table className="min-w-full bg-white border border-gray-200">
        <thead className="bg-gray-100">
          <tr>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              Department
            </th>
            <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              Staff Users
            </th>
            <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              Guest Users
            </th>
            <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              Staff Usage (GB)
            </th>
            <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              Guest Usage (GB)
            </th>
            <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              Total Usage (GB)
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200">
          {data.map((dept: DepartmentOverviewData, index: number) => (
            <tr key={dept.departmentName} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
              <td className="px-4 py-2 text-sm font-medium text-gray-900">
                {dept.departmentName}
              </td>
              <td className="px-4 py-2 text-sm text-center text-gray-700">
                {dept.staffUsers}
              </td>
              <td className="px-4 py-2 text-sm text-center text-gray-700">
                {dept.guestUsers}
              </td>
              <td className="px-4 py-2 text-sm text-center text-green-600 font-medium">
                {dept.staffUsage}
              </td>
              <td className="px-4 py-2 text-sm text-center text-yellow-600 font-medium">
                {dept.guestUsage}
              </td>
              <td className="px-4 py-2 text-sm text-center text-blue-600 font-bold">
                {dept.totalUsage}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default function AnalyticsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const defaultDates = getDefaultDates();

  // Simplified state management
  const [departments, setDepartments] = useState<PackageInfo[]>([]);
  const [selectedDepartment, setSelectedDepartment] = useState<string>("");
  const [aggregatedStats, setAggregatedStats] = useState<DepartmentStats[]>([]);
  const [individualStats, setIndividualStats] = useState<DepartmentStats[]>([]);
  const [activeStats, setActiveStats] = useState<ActiveStat[]>([]);
  const [customers, setCustomers] = useState<any[]>([]);
  const [departmentOverviewData, setDepartmentOverviewData] = useState<DepartmentOverviewData[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [startDate, setStartDate] = useState(defaultDates.start);
  const [endDate, setEndDate] = useState(defaultDates.end);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState<string>("15");
  const [lineChartData, setLineChartData] = useState<LineChartData[]>([]);
  const [lineChartDataforCount, setLineChartDataforCount] = useState<
    LineChartData[]
  >([]);

  // Modal state
  const [selectedUser, setSelectedUser] = useState<string | null>(null);
  const [userSessions, setUserSessions] = useState<DepartmentStats[]>([]);
  const [modalCurrentPage, setModalCurrentPage] = useState(1);
  const [modalItemsPerPage, setModalItemsPerPage] = useState(10);
  const [modalSearch, setModalSearch] = useState("");
  const [BWbyGroup, setBwByGroup] = useState([]);

  // Get active view from URL params
  const getActiveView = (): "aggregated" | "individual" | "active" => {
    const typeParam = searchParams.get("type");
    if (typeParam === "individualview") return "individual";
    if (typeParam === "activesessions") return "active";
    return "aggregated";
  };

  // Get active tab from URL params
  const getActiveTab = (): "overview" | "users" | "network" | "department" => {
    const tabParam = searchParams.get("tab");
    if (tabParam === "users") return "users";
    if (tabParam === "network") return "network";
    if (tabParam === "department") return "department";
    return "overview";
  };

  const [activeView, setActiveView] = useState<
    "aggregated" | "individual" | "active"
  >(getActiveView);
  const [activeTab, setActiveTab] = useState<
    "overview" | "users" | "network" | "department"
  >(getActiveTab);

  // Simplified event handlers
  const handleDepartmentChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedDepartment(e.target.value);
  };

  const handleItemsPerPageChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    setItemsPerPage(e.target.value);
    setCurrentPage(1);
  };

  const handleViewChange = (view: "aggregated" | "individual" | "active") => {
    setActiveView(view);
    setCurrentPage(1);

    const params = new URLSearchParams(searchParams.toString());
    const viewTypes = {
      individual: "individualview",
      active: "activesessions",
      aggregated: "aggregatedview",
    };
    params.set("type", viewTypes[view]);
    router.replace(`?${params.toString()}`);
  };

  const handleTabChange = (
    tab: "overview" | "users" | "network" | "department"
  ) => {
    setActiveTab(tab);
    setCurrentPage(1);

    const params = new URLSearchParams(searchParams.toString());
    params.set("tab", tab);
    router.replace(`?${params.toString()}`);
  };

  const handleUsernameClick = (username: string) => {
    const sessions = individualStats.filter(
      (session: DepartmentStats) => session.username === username
    );
    setUserSessions(sessions);
    setSelectedUser(username);
    setModalCurrentPage(1);
  };

  const closeUserSessionsModal = () => {
    setSelectedUser(null);
    setModalCurrentPage(1);
    setModalSearch("");
  };
  function bytesToGB(bytes) {
    return +(bytes / 1024 ** 3).toFixed(2);
  }
  const getBWbyGroup = (data) => {
    const groupedStats = {};

    data.forEach((entry) => {
      console.error(entry);
      const group = entry.groupname || "Unknown";
      if (!groupedStats[group]) {
        groupedStats[group] = {
          group: group,
          total: {
            upload: 0,
            download: 0,
          },
        };
      }

      groupedStats[group].total.upload += Number(entry.acctinputoctets || 0);
      groupedStats[group].total.download += Number(entry.acctoutputoctets || 0);
    });
    return Object.values(groupedStats).map((entry) => ({
      ...entry,
      total: {
        upload: bytesToGB(entry.total.upload),
        download: bytesToGB(entry.total.download),
      },
    }));
  };

  const groupBw = getBWbyGroup(aggregatedStats);

  // Simplified data processing functions
  const aggregateUserData = (rawData: DepartmentStats[]): DepartmentStats[] => {
    const userMap = new Map<string, DepartmentStats>();

    rawData.forEach((curr: DepartmentStats) => {
      const existing = userMap.get(curr.username);
      if (existing) {
        existing.acctinputoctets = (
          Number(existing.acctinputoctets) + Number(curr.acctinputoctets)
        ).toString();
        existing.acctoutputoctets = (
          Number(existing.acctoutputoctets) + Number(curr.acctoutputoctets)
        ).toString();
      } else {
        userMap.set(curr.username, { ...curr });
      }
    });

    return Array.from(userMap.values());
  };

  const processLineChartData = (
    rawData: DepartmentStats[],
    startDate: string,
    endDate: string
  ): LineChartData[] => {
    const dailyUsageMap = new Map<string, number>();

    rawData.forEach((item: DepartmentStats) => {
      const startTime = new Date(item.acctstarttime);
      // Get just the date part (YYYY-MM-DD) for daily aggregation
      const dateKey = startTime.toISOString().split("T")[0];

      const totalUsageMB =
        (Number(item.acctinputoctets) + Number(item.acctoutputoctets)) /
        1024 /
        1024;
      dailyUsageMap.set(
        dateKey,
        (dailyUsageMap.get(dateKey) || 0) + totalUsageMB
      );
    });

    const chartData = Array.from(dailyUsageMap.entries())
      .map(([timestamp, totalUsage]: [string, number]) => ({
        timestamp,
        totalUsage: parseFloat(totalUsage.toFixed(2)),
        count: 0,
      }))
      .sort(
        (a, b) =>
          new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
      );

    // Fill missing dates with zero values
    return fillMissingDates(chartData, startDate, endDate);
  };

  const processLineChartDataForCount = (
    rawData: DepartmentStats[],
    startDate: string,
    endDate: string
  ): LineChartData[] => {
    const hourlyCountMap = new Map<string, Set<string>>();

    rawData.forEach((item: DepartmentStats) => {
      const startTime = new Date(item.acctstarttime);
      startTime.setMinutes(0, 0, 0);
      const timestampKey = startTime.toISOString();

      if (!hourlyCountMap.has(timestampKey)) {
        hourlyCountMap.set(timestampKey, new Set<string>());
      }
      // Count unique users per hour
      hourlyCountMap.get(timestampKey)!.add(item.username);
    });

    const chartData = Array.from(hourlyCountMap.entries())
      .map(([timestamp, userSet]: [string, Set<string>]) => ({
        timestamp,
        totalUsage: 0,
        count: userSet.size,
      }))
      .sort(
        (a, b) =>
          new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
      );

    // Fill missing hours with zero values
    return fillMissingHours(chartData, startDate, endDate);
  };

  const fetchDepartmentStats = async () => {
    setLoading(true);
    const payload = {
      group: selectedDepartment
        ? [selectedDepartment]
        : departments.map((d: PackageInfo) => d.package_name),
      // start: `${startDate} 00:00:00`,
      // end: `${endDate} 00:00:00`,
      start: "2025-07-01 00:00:00",
      end: "2025-07-21 00:00:00"
    };

    try {
      const res = await apiClient.post("/department/stats", payload);
      const rawData = res?.data || [];

      setIndividualStats(rawData);
      setAggregatedStats(aggregateUserData(rawData));
      setLineChartData(processLineChartData(rawData, startDate, endDate));
      setLineChartDataforCount(processLineChartDataForCount(rawData, startDate, endDate));

      // Process department overview data with staff/guest separation
      // Always process the data, even if customers is empty (all will be treated as guests)
      const overviewData = processDepartmentOverviewData(rawData, departments, customers);
      console.log('Setting department overview data:', overviewData);
      setDepartmentOverviewData(overviewData);
    } catch (err) {
      console.error("Error fetching department stats:", err);
      toast.error("Error fetching department stats");
    } finally {
      setLoading(false);
    }
  };

  const fetchActiveSessionsData = async () => {
    setLoading(true);
    try {
      const payload = {
        group: departments.map((d: PackageInfo) => d.package_name),
        limit: 1,
      };

      const res = await apiClient.post("/analytics", payload);
      const analyticsData = res?.data || [];
      console.log("Active sessions data:", analyticsData);

      if (analyticsData.length > 0) {
        const latestRecord = analyticsData[0];
        const activeUsers = latestRecord.data.map((user: any) => ({
          username: user.username,
          acctstarttime: user.acctstarttime,
          acctinputoctets: user.acctinputoctets,
          acctoutputoctets: user.acctoutputoctets,
          inputMbps: latestRecord.inputMbps || 0,
          outputMbps: latestRecord.outputMbps || 0,
          count: latestRecord.count,
          recordId: latestRecord.id,
          created_at: latestRecord.created_at,
        }));
        setActiveStats(activeUsers);
      } else {
        setActiveStats([]);
      }
    } catch (err) {
      console.error("Error fetching active sessions:", err);
      toast.error("Error fetching active sessions");
      setActiveStats([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchDepartments = async () => {
    try {
      const res = await apiClient.get("/package");
      setDepartments(res?.data || []);
    } catch (err) {
      console.error("Failed to fetch departments:", err);
    }
  };

  const fetchCustomers = async () => {
    try {
      const res = await apiClient.get("/customer");
      const customerData = res?.data || [];
      console.log('Fetched customers:', customerData);
      setCustomers(customerData);
    } catch (err) {
      console.error("Failed to fetch customers:", err);
      // Set empty array on error so the component still works
      setCustomers([]);
    }
  };

  // Process department overview data with staff/guest separation
  const processDepartmentOverviewData = (
    rawData: DepartmentStats[],
    departments: PackageInfo[],
    customers: any[]
  ): DepartmentOverviewData[] => {
    console.log('Processing department overview data:', {
      rawDataLength: rawData.length,
      departmentsLength: departments.length,
      customersLength: customers.length
    });

    if (!rawData || rawData.length === 0) {
      console.log('No raw data available');
      return [];
    }

    if (!departments || departments.length === 0) {
      console.log('No departments available');
      return [];
    }

    const result = departments.map((dept) => {
      // Get all stats for this department
      const deptStats = rawData.filter((stat) => stat.groupname === dept.package_name);

      if (deptStats.length === 0) {
        console.log(`No stats found for department ${dept.package_name}`);
        return null;
      }

      // Get customers for this department (if customers data is available)
      const deptCustomers = customers.length > 0 ? customers.filter((customer) => customer.pack_id === dept.id) : [];
      const deptCustomerUsernames = new Set(deptCustomers.map((customer) => customer.username));

      console.log(`Department ${dept.package_name}:`, {
        deptStatsLength: deptStats.length,
        deptCustomersLength: deptCustomers.length,
        customerUsernames: Array.from(deptCustomerUsernames)
      });

      // Separate staff and guest data
      const staffStats = deptStats.filter((stat) => deptCustomerUsernames.has(stat.username));
      const guestStats = deptStats.filter((stat) => !deptCustomerUsernames.has(stat.username));

      // Calculate usage in GB
      const staffUsage = staffStats.reduce((sum, stat) =>
        sum + (Number(stat.acctinputoctets || 0) + Number(stat.acctoutputoctets || 0)), 0
      ) / (1024 * 1024 * 1024);

      const guestUsage = guestStats.reduce((sum, stat) =>
        sum + (Number(stat.acctinputoctets || 0) + Number(stat.acctoutputoctets || 0)), 0
      ) / (1024 * 1024 * 1024);

      // Get unique users
      const staffUsers = new Set(staffStats.map((stat) => stat.username)).size;
      const guestUsers = new Set(guestStats.map((stat) => stat.username)).size;

      const deptResult = {
        departmentName: dept.package_name,
        staffUsers,
        guestUsers,
        staffUsage: parseFloat(staffUsage.toFixed(2)),
        guestUsage: parseFloat(guestUsage.toFixed(2)),
        totalUsage: parseFloat((staffUsage + guestUsage).toFixed(2)),
        totalUsers: staffUsers + guestUsers,
      };

      console.log(`Department ${dept.package_name} result:`, deptResult);
      return deptResult;
    }).filter((dept) => dept !== null && (dept.totalUsage > 0 || dept.totalUsers > 0)) as DepartmentOverviewData[]; // Include departments with usage or users

    console.log('Final processed data:', result);
    return result;
  };

  // Effects
  useEffect(() => {
    fetchDepartments();
    fetchCustomers();
  }, []);

  useEffect(() => {
    const newView = getActiveView();
    const newTab = getActiveTab();
    if (newView !== activeView) {
      setActiveView(newView);
      setCurrentPage(1);
    }
    if (newTab !== activeTab) {
      setActiveTab(newTab);
      setCurrentPage(1);
    }
  }, [searchParams, activeView, activeTab]);

  useEffect(() => {
    if (departments.length > 0) {
      if (activeView === "active") {
        fetchActiveSessionsData();
      } else {
        fetchDepartmentStats();
      }
    }
  }, [departments, customers, activeView, selectedDepartment, startDate, endDate]);

  // Reprocess department overview data when customers are loaded
  useEffect(() => {
    if (customers.length > 0 && individualStats.length > 0 && departments.length > 0) {
      setDepartmentOverviewData(processDepartmentOverviewData(individualStats, departments, customers));
    }
  }, [customers, individualStats, departments]);

  // Handle Escape key to close modal
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape" && selectedUser) {
        closeUserSessionsModal();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [selectedUser]);

  // Get current data and pagination
  const currentData =
    activeView === "aggregated"
      ? aggregatedStats
      : activeView === "individual"
        ? individualStats
        : activeStats;

  const itemsPerPageNum =
    itemsPerPage === "all" ? currentData.length : parseInt(itemsPerPage, 10);
  const totalPages =
    itemsPerPageNum === currentData.length
      ? 1
      : Math.ceil(currentData.length / itemsPerPageNum);
  const currentStats = currentData.slice(
    (currentPage - 1) * itemsPerPageNum,
    currentPage * itemsPerPageNum
  );

  // Bar chart data formatting with sorting for top 10 users
  const formattedBarChartData: BarChartData[] = useMemo(() => {
    return aggregatedStats
      .map((stat: DepartmentStats) => ({
        name: stat.username,
        download: parseFloat(
          (Number(stat.acctoutputoctets) / (1024 * 1024 * 1024)).toFixed(2)
        ),
        upload: parseFloat(
          (Number(stat.acctinputoctets) / (1024 * 1024 * 1024)).toFixed(2)
        ),
      }))
      .sort(
        (a: BarChartData, b: BarChartData) =>
          b.download + b.upload - (a.download + a.upload)
      ) // Sort by total usage descending
      .slice(0, 10); // Take only top 10 users
  }, [aggregatedStats]);

  // Department bar chart data formatting
  const formattedDepartmentBarChartData: BarChartData[] = useMemo(() => {
    return departments
      .map((dept: PackageInfo) => {
        const deptStats = aggregatedStats.filter(
          (stat: DepartmentStats) => stat.groupname === dept.package_name
        );

        const totalDownload =
          deptStats.reduce(
            (sum: number, stat: DepartmentStats) =>
              sum + Number(stat.acctoutputoctets),
            0
          ) /
          (1024 * 1024 * 1024); // Convert to GB

        const totalUpload =
          deptStats.reduce(
            (sum: number, stat: DepartmentStats) =>
              sum + Number(stat.acctinputoctets),
            0
          ) /
          (1024 * 1024 * 1024); // Convert to GB

        return {
          name: dept.package_name,
          download: parseFloat(totalDownload.toFixed(2)),
          upload: parseFloat(totalUpload.toFixed(2)),
        };
      })
      .filter((dept: BarChartData) => dept.download > 0 || dept.upload > 0) // Only include departments with usage
      .sort(
        (a: BarChartData, b: BarChartData) =>
          b.download + b.upload - (a.download + a.upload)
      ) // Sort by total usage descending
      .slice(0, 10); // Take only top 10 departments
  }, [departments, aggregatedStats]);

  const sortedDepartments = [...departments]
    .map((dept: PackageInfo) => {
      const deptStats = aggregatedStats.filter(
        (stat: DepartmentStats) => stat.groupname === dept.package_name
      );
      const totalUsage =
        deptStats.reduce(
          (sum: number, stat: DepartmentStats) =>
            sum +
            (Number(stat.acctinputoctets) + Number(stat.acctoutputoctets)),
          0
        ) /
        (1024 * 1024 * 1024); // GB

      return {
        ...dept,
        totalUsage,
        userCount: deptStats.length,
      };
    })
    .sort((a, b) => b.totalUsage - a.totalUsage);

  return (
    <div className="p-5 sm:p-5">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center flex-wrap gap-3">
        <h1 className="text-lg sm:text-lg font-bold sm:mb-3 text-wrap">
          Analytics Dashboard
        </h1>
      </div>

      {/* Stats Cards */}
      <div className="mb-3 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
        <div
          className="p-3 border-b bg-blue-700 rounded-lg border border-blue-300"
          onClick={() =>
            router.push("/app/analytics?type=aggregatedview&tab=users")
          }
        >
          <div className="flex flex-col sm:flex-row items-center justify-between w-full">
            <div className="flex flex-col sm:flex-row items-center gap-3 cursor-pointer">
              <h1 className="text-center text-white">Total Sessions</h1>
              <strong className="text-white">{individualStats.length}</strong>
            </div>
            <Sessions className="text-white" />
          </div>
        </div>

        <div
          className="p-3 border-b bg-green-700 rounded-lg border border-green-300"
          onClick={() =>
            router.push("/app/analytics?type=activesessions&tab=users")
          }
        >
          <div className="flex flex-col sm:flex-row items-center justify-between w-full">
            <div className="flex flex-col sm:flex-row items-center gap-3 cursor-pointer">
              <h1 className="text-center text-white"> Online Users </h1>
              <strong className="text-white">
                {activeStats.length > 0 ? activeStats[0]?.count || 0 : 0}
              </strong>
            </div>
            <Users className="text-white" />
          </div>
        </div>

        <div className="p-3 border-b bg-yellow-500 rounded-lg border border-yellow-300">
          <div className="flex flex-col sm:flex-row items-center justify-between w-full"
            onClick={() => router.push('/app/analytics?type=aggregatedview&tab=network')}>
            <div className="flex flex-col sm:flex-row items-center gap-3">
              <h1 className="text-center text-white">Total Network Usage (GB) </h1>
              <strong className="text-white">{(aggregatedStats.reduce((total: number, stat: DepartmentStats) =>
                total + (Number(stat.acctinputoctets) + Number(stat.acctoutputoctets)), 0) / (1024 * 1024 * 1024)).toFixed(2)}</strong>
            </div>
            <Network className="text-white" />
          </div>
        </div>
      </div>

      {/* Main Navigation Tabs */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {["overview", "users", "network", "department"].map((tab) => (
              <button
                key={tab}
                onClick={() =>
                  handleTabChange(
                    tab as "overview" | "users" | "network" | "department"
                  )
                }
                className={`py-2 px-1 border-b-2 font-medium text-sm capitalize ${activeTab === tab
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
              >
                {tab}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === "overview" && (
          <>
            {/* Department Overview Summary */}
            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200 mb-6">
              <DepartmentOverviewTable data={departmentOverviewData} loading={loading} />
            </div>

            {/* Charts Grid */}
            <div className="grid grid-cols-2 gap-3 mb-3">
              <div className="bg-white p-3 rounded-lg h-[400px] flex items-center justify-center border border-yellow-300">
                <AnalyticsBarChart data={groupBw} />
              </div>
              <div className="bg-white p-3 rounded-lg h-[400px] flex items-center justify-center border border-green-300">
                <AnalyticsLineChart
                  data={lineChartData}
                  startDate={startDate}
                  endDate={endDate}
                />
              </div>
            </div>

            {/* Additional Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              <div className="bg-white p-4 rounded-lg h-[400px] flex items-center justify-center border border-purple-300">
                <AnalyticsLineChartforCount data={lineChartDataforCount} startDate={startDate} endDate={endDate} />
              </div>
              <div className="bg-white p-4 rounded-lg h-[400px] flex items-center justify-center border border-yellow-300">
                <AnalyticsBarChart data={formattedBarChartData} />
              </div>
            </div>

            {/* Staff vs Guest Pie Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              <div className="bg-white p-4 rounded-lg h-[400px] flex items-center justify-center border border-green-300">
                <StaffPieChart data={individualStats} customers={customers} />
              </div>
              <div className="bg-white p-4 rounded-lg h-[400px] flex items-center justify-center border border-orange-300">
                <GuestPieChart data={individualStats} customers={customers} />
              </div>
            </div>
          </>
        )}

        {activeTab === "users" && (
          <div className="space-y-6">
            {/* User Analytics Summary */}
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h2 className="text-xl font-semibold mb-4">User Analytics Summary</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {(
                      aggregatedStats.reduce(
                        (sum: number, user: DepartmentStats) =>
                          sum +
                          (Number(user.acctinputoctets) +
                            Number(user.acctoutputoctets)),
                        0
                      ) /
                      (1024 * 1024 * 1024)
                    ).toFixed(2)}{" "}
                    GB
                  </div>
                  <div className="text-sm text-blue-600">
                    Total Bandwidth Usage
                  </div>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {individualStats.length}
                  </div>
                  <div className="text-sm text-green-600">Total Sessions</div>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    {aggregatedStats.length}
                  </div>
                  <div className="text-sm text-purple-600">Unique Users</div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div className="bg-white p-3 rounded-lg h-[400px] flex items-center justify-center border border-yellow-300">
                <AnalyticsBarChart data={formattedBarChartData} />
              </div>
              <div className="bg-white p-3 rounded-lg h-[400px] flex items-center justify-center border border-blue-300">
                <AnalyticsLineChartforCount
                  data={lineChartDataforCount}
                  startDate={startDate}
                  endDate={endDate}
                />
              </div>
            </div>

            {/* View Toggle Tabs for Users */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-3">
              <div className="p-3 border-b">
                <div className="flex flex-col sm:flex-row gap-3 w-full">
                  <button
                    onClick={() => handleViewChange("aggregated")}
                    className={`px-4 py-2 rounded-full border w-full sm:w-auto h-10 ${activeView === "aggregated"
                        ? "bg-blue-600 text-white"
                        : "border-gray-300 text-gray-700 bg-white"
                      }`}
                  >
                    Aggregated View
                  </button>
                  {/* <button
                    onClick={() => handleViewChange("individual")}
                    className={`px-4 py-2 rounded-full border w-full sm:w-auto h-10 ${activeView === "individual"
                      ? "bg-blue-600 text-white"
                      : "border-gray-300 text-gray-700 bg-white"
                      }`}
                  >
                    Individual Sessions
                  </button> */}
                  <button
                    onClick={() => handleViewChange("active")}
                    className={`px-4 py-2 rounded-full border w-full sm:w-auto h-10 ${activeView === "active"
                        ? "bg-blue-600 text-white"
                        : "border-gray-300 text-gray-700 bg-white"
                      }`}
                  >
                    Active Sessions
                  </button>
                </div>
              </div>
            </div>

            {/* Filters - Hidden for Active Sessions */}
            {activeView !== "active" && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-3">
                <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-3 ">
                  {/* Left Section - Department Filter and Date Range */}
                  <div className="flex flex-col md:flex-row items-start md:items-center gap-3 w-full lg:w-auto">
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 text-xs font-medium text-gray-700 w-full sm:w-auto">
                      <span>Department:</span>
                      <select
                        value={selectedDepartment}
                        onChange={handleDepartmentChange}
                        className="border border-gray-300 rounded-md px-2 py-1.5 text-xs bg-white shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 min-w-[180px] w-full sm:w-auto"
                      >
                        <option value="">All Departments</option>
                        {departments.map((dept: PackageInfo) => (
                          <option key={dept.id} value={dept.package_name}>
                            {dept.package_name}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Date Range Section */}
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 w-full sm:w-auto">
                      <span className="text-xs font-medium text-gray-700">
                        Filter by Date:
                      </span>
                      <div className="flex flex-col sm:flex-row items-center gap-2 w-full sm:w-auto">
                        <div className="flex flex-col w-full sm:w-auto">
                          <input
                            type="date"
                            value={startDate}
                            onChange={(
                              e: React.ChangeEvent<HTMLInputElement>
                            ) => setStartDate(e.target.value)}
                            className="border border-gray-300 rounded-md px-2 py-1.5 text-xs bg-white shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 w-full"
                          />
                        </div>
                        <div className="flex items-center justify-center px-2">
                          <span className="text-gray-400 text-sm">to</span>
                        </div>
                        <div className="flex flex-col w-full sm:w-auto">
                          <input
                            type="date"
                            value={endDate}
                            onChange={(
                              e: React.ChangeEvent<HTMLInputElement>
                            ) => setEndDate(e.target.value)}
                            className="border border-gray-300 rounded-md px-2 py-1.5 text-xs bg-white shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 w-full"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Right Section - Show Pages and View Info */}
                  <div className="flex flex-col lg:flex-row items-end lg:items-center gap-3">
                    {/* View Status Indicator */}
                    <div className="flex items-center gap-2">
                      <div className="text-xs text-gray-600 bg-gray-100 px-3 py-1.5 rounded-md">
                        <span className="font-medium">
                          {activeView === "aggregated"
                            ? "Aggregated"
                            : activeView === "individual"
                              ? "Individual"
                              : "Active"}{" "}
                          View
                        </span>
                        <span className="ml-2 text-blue-600 font-semibold">
                          ({currentData.length} records)
                        </span>
                      </div>
                    </div>

                    {/* Items per page */}
                    <div className="flex items-center gap-2 text-xs">
                      <span>Show</span>
                      <select
                        value={itemsPerPage}
                        onChange={handleItemsPerPageChange}
                        className="border border-gray-300 rounded-md px-2 py-1.5 text-xs focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="15">15</option>
                        <option value="30">30</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                        <option value="all">All</option>
                      </select>
                      <span>entries</span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* View Status for Active Sessions */}
            {activeView === "active" && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-3">
                <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-3">
                  <div className="text-xs text-gray-600 bg-gray-100 px-3 py-1.5 rounded-lg">
                    <span className="font-medium">Active Sessions</span>
                    <span className="ml-2 text-blue-600 font-semibold">
                      ({currentData.length} online clients)
                    </span>
                    <span className="ml-2 text-gray-500">
                      {currentData.length > 0 &&
                        (currentData[0] as any).created_at
                        ? `(${new Date(
                          (currentData[0] as any).created_at
                        ).toLocaleString()})`
                        : ""}
                    </span>
                  </div>
                </div>
              </div>
            )}

            <div className="bg-white rounded-lg shadow p-4 overflow-x-auto">
              {loading ? (
                <div className="text-center py-8 text-gray-500">
                  Loading analytics data...
                </div>
              ) : currentData?.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  No analytics data found for the selected criteria.
                </div>
              ) : (
                <div className="w-full overflow-x-auto">
                  <table className="min-w-full lg:min-w-max w-full">
                    <thead className="bg-gray-200 text-left text-xs uppercase">
                      <tr>
                        <th className="px-4 py-2">S.N.</th>
                        <th className="px-4 py-2">Username</th>
                        {activeView === "active" && (
                          <>
                            <th className="px-4 py-2">Start Time</th>
                            {/* {activeView === "individual" && (
                              <th className="px-4 py-2">Stop Time</th>
                            )} */}
                            {activeView === "active" && (
                              <th className="px-4 py-2">Duration</th>
                            )}
                          </>
                        )}
                        {activeView === "aggregated" && (
                          <th className="px-4 py-2">Sessions</th>
                        )}

                        <th className="px-4 py-2">Upload</th>
                        <th className="px-4 py-2">Download</th>
                        {activeView === "active" && (
                          <>
                            <th className="px-4 py-2">Upload Speed (Mbps)</th>
                            <th className="px-4 py-2">Download Speed (Mbps)</th>
                          </>
                        )}
                        {activeView !== "active" && (
                          <th className="px-4 py-2">Department</th>
                        )}
                      </tr>
                    </thead>
                    <tbody>
                      {currentStats.map(
                        (item: DepartmentStats, index: number) => (
                          <tr
                            key={`${item.username}-${item.acctstarttime}-${index}`}
                            className="border-b hover:bg-gray-50"
                          >
                            <td className="px-4 py-2 text-xs">
                              {(currentPage - 1) * itemsPerPageNum + index + 1}
                            </td>
                            <td className="px-4 py-2 text-xs">
                              {activeView === "aggregated" ? (
                                <button
                                  onClick={() =>
                                    handleUsernameClick(item.username)
                                  }
                                  className="text-blue-600 font-medium hover:text-blue-800 hover:underline cursor-pointer"
                                >
                                  {item.username}
                                </button>
                              ) : (
                                <span className="text-blue-600 font-medium">
                                  {item.username}
                                </span>
                              )}
                            </td>
                            {activeView === "active" && (
                              <>
                                <td className="px-4 py-2 text-xs">
                                  {new Date(item.acctstarttime).toLocaleString(
                                    "en-US",
                                    {
                                      year: "numeric",
                                      month: "2-digit",
                                      day: "2-digit",
                                      hour: "2-digit",
                                      minute: "2-digit",
                                      second: "2-digit",
                                      hour12: false,
                                    }
                                  )}
                                </td>
                                {/* {activeView === "individual" && (
                                  <td className="px-4 py-2 text-xs">
                                    {item.acctstoptime
                                      ? new Date(item.acctstoptime).toLocaleString(
                                        "en-US",
                                        {
                                          year: "numeric",
                                          month: "2-digit",
                                          day: "2-digit",
                                          hour: "2-digit",
                                          minute: "2-digit",
                                          second: "2-digit",
                                          hour12: false,
                                        }
                                      )
                                      : "Active"}
                                  </td>
                                )} */}
                                {activeView === "active" && (
                                  <td className="px-4 py-2 text-xs">
                                    {(() => {
                                      const startTime = new Date(
                                        item.acctstarttime
                                      );
                                      const now = new Date();
                                      const durationMinutes = Math.round(
                                        (now.getTime() - startTime.getTime()) /
                                        1000 /
                                        60
                                      );
                                      return `${durationMinutes} min`;
                                    })()}
                                  </td>
                                )}
                              </>
                            )}
                            {activeView === "aggregated" && (
                              <td className="px-4 py-2 text-xs">
                                {
                                  individualStats.filter(
                                    (session: DepartmentStats) =>
                                      session.username === item.username
                                  ).length
                                }
                              </td>
                            )}

                            <td className="px-4 py-2 text-xs">
                              {(
                                Number(item.acctinputoctets) /
                                1024 /
                                1024
                              ).toFixed(2)}{" "}
                              MB
                            </td>
                            <td className="px-4 py-2 text-xs">
                              {(
                                Number(item.acctoutputoctets) /
                                1024 /
                                1024
                              ).toFixed(2)}{" "}
                              MB
                            </td>
                            {activeView === "active" && (
                              <>
                                <td className="px-4 py-2 text-xs">
                                  {(item as any).inputMbps
                                    ? (item as any).inputMbps.toFixed(4)
                                    : "0.0000"}
                                </td>
                                <td className="px-4 py-2 text-xs">
                                  {(item as any).outputMbps
                                    ? (item as any).outputMbps.toFixed(4)
                                    : "0.0000"}
                                </td>
                              </>
                            )}
                            {activeView !== "active" && (
                              <td className="px-4 py-2 text-xs">
                                {item.groupname}
                              </td>
                            )}
                          </tr>
                        )
                      )}
                    </tbody>
                  </table>
                </div>
              )}

              {/* Pagination controls */}
              {totalPages > 1 && (
                <div className="flex items-center gap-1 justify-left mt-4">
                  <Button
                    className="rounded-full w-8 h-8 p-0 flex items-center justify-center"
                    size="sm"
                    onClick={() =>
                      setCurrentPage((p: number) => Math.max(p - 1, 1))
                    }
                    disabled={currentPage === 1}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <span className="text-[12px] px-2">
                    Page {currentPage} of {totalPages}
                  </span>
                  <Button
                    className="rounded-full w-8 h-8 p-0 flex items-center justify-center"
                    size="sm"
                    onClick={() =>
                      setCurrentPage((p: number) => Math.min(p + 1, totalPages))
                    }
                    disabled={currentPage === totalPages}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              )}

              {/* User Sessions Modal */}
              {selectedUser && (
                <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4">
                  <div className="bg-white rounded-lg p-6 max-h-[90vh] overflow-y-auto w-full max-w-6xl relative">
                    <div className="flex justify-between items-center mb-4">
                      <h2 className="text-xl font-bold">
                        Individual Sessions for: {selectedUser}
                      </h2>
                      <button
                        onClick={closeUserSessionsModal}
                        className="text-gray-500 hover:text-gray-700 text-2xl font-bold"
                      >
                        ×
                      </button>
                    </div>

                    {/* Search and Controls */}
                    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 mb-4">
                      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                        <div className="text-sm text-gray-600">
                          Total Sessions:{" "}
                          <span className="font-semibold">
                            {userSessions.length}
                          </span>
                        </div>

                        <input
                          type="text"
                          placeholder="Search ..."
                          value={modalSearch}
                          onChange={(
                            e: React.ChangeEvent<HTMLInputElement>
                          ) => {
                            setModalSearch(e.target.value);
                            setModalCurrentPage(1);
                          }}
                          className="w-full sm:w-64 px-3 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>

                      <div className="flex items-center gap-2 text-xs">
                        <span>Show</span>
                        <select
                          value={modalItemsPerPage}
                          onChange={(
                            e: React.ChangeEvent<HTMLSelectElement>
                          ) => {
                            setModalItemsPerPage(Number(e.target.value));
                            setModalCurrentPage(1);
                          }}
                          className="border border-gray-300 rounded px-2 py-1 text-xs focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value={10}>10</option>
                          <option value={25}>25</option>
                          <option value={50}>50</option>
                          <option value={100}>100</option>
                        </select>
                        <span>per page</span>
                      </div>
                    </div>

                    <div className="overflow-x-auto">
                      {(() => {
                        // Filter sessions based on search
                        const filteredSessions = userSessions.filter(
                          (session: DepartmentStats) => {
                            if (!modalSearch) return true;
                            const searchLower = modalSearch.toLowerCase();
                            return (
                              session.framedipaddress
                                ?.toLowerCase()
                                .includes(searchLower) ||
                              session.callingstationid
                                ?.toLowerCase()
                                .includes(searchLower) ||
                              session.acctterminatecause
                                ?.toLowerCase()
                                .includes(searchLower)
                            );
                          }
                        );

                        // Get current page sessions
                        const currentPageSessions = filteredSessions.slice(
                          (modalCurrentPage - 1) * modalItemsPerPage,
                          modalCurrentPage * modalItemsPerPage
                        );

                        return (
                          <>
                            <table className="min-w-full text-xs">
                              <thead className="bg-gray-200 text-left uppercase">
                                <tr>
                                  <th className="px-3 py-2">S.N.</th>
                                  <th className="px-3 py-2">Start Time</th>
                                  <th className="px-3 py-2">Stop Time</th>
                                  <th className="px-3 py-2">Duration</th>
                                  <th className="px-3 py-2">Department</th>
                                  <th className="px-3 py-2">Upload (MB)</th>
                                  <th className="px-3 py-2">Download (MB)</th>
                                  <th className="px-3 py-2">IP Address</th>
                                  <th className="px-3 py-2">MAC Address</th>
                                  <th className="px-3 py-2">Terminate Cause</th>
                                </tr>
                              </thead>
                              <tbody>
                                {currentPageSessions.map(
                                  (session: DepartmentStats, index: number) => {
                                    const startTime = new Date(
                                      session.acctstarttime
                                    );
                                    const stopTime = session.acctstoptime
                                      ? new Date(session.acctstoptime)
                                      : null;
                                    const duration = stopTime
                                      ? Math.round(
                                        (stopTime.getTime() -
                                          startTime.getTime()) /
                                        1000 /
                                        60
                                      ) // minutes
                                      : null;

                                    return (
                                      <tr
                                        key={`${session.acctstarttime}-${index}`}
                                        className="border-b hover:bg-gray-50"
                                      >
                                        <td className="px-3 py-2">
                                          {(modalCurrentPage - 1) *
                                            modalItemsPerPage +
                                            index +
                                            1}
                                        </td>
                                        <td className="px-3 py-2">
                                          {startTime.toLocaleString("en-US", {
                                            year: "numeric",
                                            month: "2-digit",
                                            day: "2-digit",
                                            hour: "2-digit",
                                            minute: "2-digit",
                                            second: "2-digit",
                                            hour12: false,
                                          })}
                                        </td>
                                        <td className="px-3 py-2">
                                          {stopTime
                                            ? stopTime.toLocaleString("en-US", {
                                              year: "numeric",
                                              month: "2-digit",
                                              day: "2-digit",
                                              hour: "2-digit",
                                              minute: "2-digit",
                                              second: "2-digit",
                                              hour12: false,
                                            })
                                            : "Active"}
                                        </td>
                                        <td className="px-3 py-2">
                                          {duration
                                            ? `${duration} min`
                                            : "Active"}
                                        </td>
                                        <td className="px-3 py-2">
                                          {session.groupname}
                                        </td>
                                        <td className="px-3 py-2">
                                          {(
                                            Number(session.acctinputoctets) /
                                            1024 /
                                            1024
                                          ).toFixed(2)}
                                        </td>
                                        <td className="px-3 py-2">
                                          {(
                                            Number(session.acctoutputoctets) /
                                            1024 /
                                            1024
                                          ).toFixed(2)}
                                        </td>
                                        <td className="px-3 py-2">
                                          {session.framedipaddress}
                                        </td>
                                        <td className="px-3 py-2">
                                          {session.callingstationid}
                                        </td>
                                        <td className="px-3 py-2">
                                          {session.acctterminatecause ||
                                            "Active"}
                                        </td>
                                      </tr>
                                    );
                                  }
                                )}
                              </tbody>
                            </table>

                            {/* Modal Pagination */}
                            {Math.ceil(
                              filteredSessions.length / modalItemsPerPage
                            ) > 1 && (
                                <div className="flex items-center justify-between mt-4 pt-4 border-t">
                                  <div className="text-xs text-gray-600">
                                    Showing{" "}
                                    {(modalCurrentPage - 1) * modalItemsPerPage +
                                      1}{" "}
                                    to{" "}
                                    {Math.min(
                                      modalCurrentPage * modalItemsPerPage,
                                      filteredSessions.length
                                    )}{" "}
                                    of {filteredSessions.length} sessions
                                  </div>

                                  <div className="flex items-center gap-1">
                                    <Button
                                      className="rounded-full w-8 h-8 p-0 flex items-center justify-center"
                                      size="sm"
                                      onClick={() =>
                                        setModalCurrentPage((p: number) =>
                                          Math.max(p - 1, 1)
                                        )
                                      }
                                      disabled={modalCurrentPage === 1}
                                    >
                                      <ChevronLeft className="h-4 w-4" />
                                    </Button>
                                    <span className="text-xs px-2">
                                      Page {modalCurrentPage} of{" "}
                                      {Math.ceil(
                                        filteredSessions.length /
                                        modalItemsPerPage
                                      )}
                                    </span>
                                    <Button
                                      className="rounded-full w-8 h-8 p-0 flex items-center justify-center"
                                      size="sm"
                                      onClick={() =>
                                        setModalCurrentPage((p: number) =>
                                          Math.min(
                                            p + 1,
                                            Math.ceil(
                                              filteredSessions.length /
                                              modalItemsPerPage
                                            )
                                          )
                                        )
                                      }
                                      disabled={
                                        modalCurrentPage ===
                                        Math.ceil(
                                          filteredSessions.length /
                                          modalItemsPerPage
                                        )
                                      }
                                    >
                                      <ChevronRight className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </div>
                              )}
                          </>
                        );
                      })()}
                    </div>

                    <div className="mt-4 flex justify-end">
                      <button
                        onClick={closeUserSessionsModal}
                        className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                      >
                        Close
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === "network" && (
          <div className="space-y-6">
            {/* Network Overview */}
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h2 className="text-xl font-semibold mb-4">
                Network Performance Overview
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-indigo-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-indigo-600">
                    {departments.length}
                  </div>
                  <div className="text-sm text-indigo-600">
                    Total Departments
                  </div>
                </div>
                <div className="bg-teal-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-teal-600">
                    {activeStats.length > 0 ? activeStats[0]?.count || 0 : 0}
                  </div>
                  <div className="text-sm text-teal-600">
                    Current Active Sessions
                  </div>
                </div>
                <div className="bg-rose-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-rose-600">
                    {(
                      aggregatedStats.reduce(
                        (sum: number, stat: DepartmentStats) =>
                          sum +
                          (Number(stat.acctinputoctets) +
                            Number(stat.acctoutputoctets)),
                        0
                      ) /
                      (1024 * 1024)
                    ).toFixed(2)}{" "}
                    MB
                  </div>
                  <div className="text-sm text-rose-600">
                    Total Bandwidth Usage
                  </div>
                </div>
              </div>
            </div>

            {/* Network Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h2 className="text-xl font-semibold mb-4">
                  Data Usage Over Time
                </h2>
                <div className="h-[320px]">
                  <AnalyticsLineChart
                    data={lineChartData}
                    startDate={startDate}
                    endDate={endDate}
                  />
                </div>
              </div>
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h2 className="text-xl font-semibold mb-4">
                  Active Users Count
                </h2>
                <div className="h-[320px]">
                  <AnalyticsLineChartforCount
                    data={lineChartDataforCount}
                    startDate={startDate}
                    endDate={endDate}
                  />
                </div>
              </div>
            </div>

            {/* Department Analytics */}
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h2 className="text-xl font-semibold mb-4">
                Department Analytics
              </h2>
              <div className="bg-white rounded-lg shadow p-4 overflow-x-auto">
                <table className="min-w-max w-full">
                  <thead className="bg-gray-200 text-left text-xs uppercase">
                    <tr>
                      <th className="px-4 py-2">S.N.</th>
                      <th className="px-4 py-2">Department</th>
                      <th className="px-4 py-2">Total Usage (GB)</th>
                      <th className="px-4 py-2">Total Staff</th>
                    </tr>
                  </thead>
                  <tbody>
                    {departments.length === 0 ? (
                      <tr>
                        <td
                          colSpan={4}
                          className="text-center py-8 text-gray-500"
                        >
                          No departments found.
                        </td>
                      </tr>
                    ) : (
                      sortedDepartments.map(
                        (dept: PackageInfo, index: number) => {
                          const deptStats = aggregatedStats.filter(
                            (stat: DepartmentStats) =>
                              stat.groupname === dept.package_name
                          );
                          const deptTotalUsage =
                            deptStats.reduce(
                              (sum: number, stat: DepartmentStats) =>
                                sum +
                                (Number(stat.acctinputoctets) +
                                  Number(stat.acctoutputoctets)),
                              0
                            ) /
                            (1024 * 1024 * 1024); // convert to GB

                          return (
                            <tr
                              key={dept.id}
                              className="border-b hover:bg-gray-50 text-sm"
                            >
                              <td className="px-4 py-2">{index + 1}</td>
                              <td className="px-4 py-2">{dept.package_name}</td>
                              <td className="px-4 py-2">
                                {deptTotalUsage.toFixed(2)}
                              </td>
                              <td className="px-4 py-2">{deptStats.length}</td>
                            </tr>
                          );
                        }
                      )
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {activeTab === "department" && (
          <div className="space-y-6">
            {/* Department Overview */}
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h2 className="text-xl font-semibold mb-4">Department Overview</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div className="bg-indigo-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-indigo-600">
                    {departments.length}
                  </div>
                  <div className="text-sm text-indigo-600">
                    Total Departments
                  </div>
                </div>
                {/* <div className="bg-teal-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-teal-600">
                    {activeStats.length > 0 ? activeStats[0]?.count || 0 : 0}
                  </div>
                  <div className="text-sm text-teal-600">Current Active Sessions</div>
                </div> */}
                <div className="bg-rose-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-rose-600">
                    {(
                      aggregatedStats.reduce(
                        (sum: number, stat: DepartmentStats) =>
                          sum +
                          (Number(stat.acctinputoctets) +
                            Number(stat.acctoutputoctets)),
                        0
                      ) /
                      (1024 * 1024)
                    ).toFixed(2)}{" "}
                    MB
                  </div>
                  <div className="text-sm text-rose-600">
                    Total Bandwidth Usage
                  </div>
                </div>
              </div>
            </div>

            {/* Department Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h2 className="text-xl font-semibold mb-4">
                  Data Usage Over Time
                </h2>
                <div className="h-[320px]">
                  <AnalyticsLineChart
                    data={lineChartData}
                    startDate={startDate}
                    endDate={endDate}
                  />
                </div>
              </div>
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h2 className="text-xl font-semibold mb-4">
                  Total Bandwidth Usage per Department{" "}
                </h2>
                <div className="bg-white p-3 rounded-lg h-[320px] flex items-center justify-center border border-purple-300">
                  <DepartmentBarChart data={formattedDepartmentBarChartData} />
                </div>
              </div>
            </div>

            {/* Department Analytics */}
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h2 className="text-xl font-semibold mb-4">
                Department Analytics
              </h2>
              <div className="bg-white rounded-lg shadow p-4 overflow-x-auto">
                <table className="min-w-max w-full">
                  <thead className="bg-gray-200 text-left text-xs uppercase">
                    <tr>
                      <th className="px-4 py-2">S.N.</th>
                      <th className="px-4 py-2">Department</th>
                      <th className="px-4 py-2">Total Usage (GB)</th>
                      <th className="px-4 py-2">Total Staff</th>
                    </tr>
                  </thead>
                  <tbody>
                    {departments.length === 0 ? (
                      <tr>
                        <td
                          colSpan={4}
                          className="text-center py-8 text-gray-500"
                        >
                          No departments found.
                        </td>
                      </tr>
                    ) : (
                      sortedDepartments.map(
                        (dept: PackageInfo, index: number) => {
                          const deptStats = aggregatedStats.filter(
                            (stat: DepartmentStats) =>
                              stat.groupname === dept.package_name
                          );
                          const deptTotalUsage =
                            deptStats.reduce(
                              (sum: number, stat: DepartmentStats) =>
                                sum +
                                (Number(stat.acctinputoctets) +
                                  Number(stat.acctoutputoctets)),
                              0
                            ) /
                            (1024 * 1024 * 1024); // convert to GB

                          return (
                            <tr
                              key={dept.id}
                              className="border-b hover:bg-gray-50 text-sm"
                            >
                              <td className="px-4 py-2">{index + 1}</td>
                              <td className="px-4 py-2">{dept.package_name}</td>
                              <td className="px-4 py-2">
                                {deptTotalUsage.toFixed(2)}
                              </td>
                              <td className="px-4 py-2">{deptStats.length}</td>
                            </tr>
                          );
                        }
                      )
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
