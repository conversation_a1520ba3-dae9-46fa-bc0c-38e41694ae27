"use client";

import React, { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Eye, EyeOff } from "@/components/icons/list";
import { Mail } from "lucide-react";
import { toast } from "sonner";
import apiClient from "@/lib/apiClient";

export default function ManageMail({
  initialData,
  onSubmit,
}: {
  initialData: any;
  onSubmit: (mail: any) => void;
}) {
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    host: "",
    port: "",
    secure: false,
  });

  const [isEditMode, setIsEditMode] = useState(false);
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPasswordText, setShowConfirmPasswordText] = useState(false);
  const [isValid, setIsValid] = useState(false);
  const [passwordMismatch, setPasswordMismatch] = useState(false);
  const [loading, setLoadingTrue] = useState(true);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    if (initialData) {
      const emailData = initialData;
      setFormData({
        email: emailData.email || "",
        password: "",
        host: emailData.host || "",
        port: emailData.port || "",
        secure: emailData.secure,
      });
      setIsEditMode(true);
    } else {
      // No initial data means we're in add mode
      setFormData({
        email: "",
        password: "",
        host: "",
        port: "",
        secure: false,
      });
      setIsEditMode(false);
    }
    setLoadingTrue(false);
  }, [initialData]);

  const getChangedFields = () => {
    const diff: Record<string, any> = {};
    for (const key in formData) {
      if (formData[key] !== initialData[key]) {
        if (key === "password" && formData[key] === "") continue;
        if (key === "secure") {
          diff[key] = String(formData[key]);
        } else {
          diff[key] = formData[key];
        }
      }
    }
    return diff;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleChangePort = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    if (/^\d*$/.test(value)) {
      setFormData((prev) => ({ ...prev, port: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (passwordMismatch) {
      toast.error("Passwords do not match");
      return;
    }

    const payload = {
      key: "email",
      value: {
        email: formData.email.trim(),
        password: formData.password || undefined,
        host: formData.host.trim(),
        port: formData.port,
        secure: String(formData.secure),
      },
    };

    try {
      if (!isEditMode) {
        const res = await apiClient.post("/admin/email", payload);
        const json = await res.json();

        if (res.ok && json.success) {
          onSubmit?.(formData);
          toast.success(json.message || "Email config added successfully");
        } else {
          toast.error(json.message || "Error saving email config");
        }
      } else {
        const changedFields = getChangedFields();
        const emailPayload = { key: "email", value: changedFields };
        const res = await apiClient.patch("/admin/email", emailPayload);
        const json = res?.data;

        if (json?.success) {
          toast.success(json?.message || "Email config updated successfully");
        } else {
          toast.error(json?.message || "Error updating email config");
        }
      }
    } catch (err) {
      console.error(err);
      toast.error("Internal server error");
    }
  };

  useEffect(() => {
    const emailValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email.trim());
    const hostValid = formData.host.trim() !== "";
    const portValid = /^\d+$/.test(formData.port) && Number(formData.port) > 0;
    const passwordValid =
      formData.password === confirmPassword &&
      (!formData.password ||
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^A-Za-z\d]).{8,}$/.test(
          formData.password
        ));

    setIsValid(emailValid && hostValid && portValid && passwordValid);
    setPasswordMismatch(
      confirmPassword.trim() !== "" && formData.password !== confirmPassword
    );

    // Check for changes only in edit mode
    if (isEditMode && initialData) {
      const changedFields = getChangedFields();
      setHasChanges(Object.keys(changedFields).length > 0);
    } else if (!isEditMode) {
      // In add mode, always allow submission if form is valid
      setHasChanges(true);
    }
  }, [formData, confirmPassword, isEditMode, initialData]);

  if (loading)
    return <div className="text-center py-10">Loading email config...</div>;

  return (
    <div className="bg-white border border-gray-200 rounded-xl shadow-sm">
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <div className="bg-blue-100 p-2 rounded-lg text-blue-600">
            <Mail className="w-5 h-5" />
          </div>
          <div>
            <h1 className="text-lg font-extrabold text-left">
              {isEditMode ? "Update Mail" : "Add Mail"}
            </h1>
            <p className="text-black text-sm text-center">
              {isEditMode
                ? "Update existing email configuration"
                : "Add email configuration to enable mailing"}
            </p>
          </div>
        </div>
      </div>
      <div className="p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <label className="text-xs font-semibold text-gray-700">Email</label>
            <Input
              value={formData.email}
              name="email"
              onChange={handleChange}
              placeholder="Email Address"
              required
              pattern="[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,}$"
            />
            {formData.email &&
              !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email.trim()) && (
                <p className="text-xs text-red-500">
                  Please enter a valid email address
                </p>
              )}
          </div>

          <div className="space-y-2">
            <label className="text-xs font-semibold text-gray-700">
              Password
            </label>
            <div className="relative">
              <Input
                type={showPassword ? "text" : "password"}
                name="password"
                value={formData.password}
                onChange={handleChange}
                placeholder={
                  isEditMode
                    ? "New Password (leave blank to keep current)"
                    : "Password"
                }
                className="pr-12"
                minLength={8}
                required={!isEditMode}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-2 top-1/2 -translate-y-1/2 h-8"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </Button>
            </div>
            {formData.password &&
              !/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^A-Za-z\d]).{8,}$/.test(
                formData.password
              ) && (
                <p className="text-xs text-red-500">
                  Password must contain at least 8 characters with uppercase,
                  lowercase, number, and special character.
                </p>
              )}
          </div>

          <div className="space-y-2">
            <label className="text-xs font-semibold text-gray-700">
              Confirm Password
            </label>
            <div className="relative">
              <Input
                type={showConfirmPasswordText ? "text" : "password"}
                placeholder="Confirm Password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className={passwordMismatch ? "border-red-500" : ""}
                required={!isEditMode || !!formData.password}
                minLength={8}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-2 top-1/2 -translate-y-1/2 h-8"
                onClick={() => setShowConfirmPasswordText(!showConfirmPasswordText)}
              >
                {showConfirmPasswordText ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
            {passwordMismatch && (
              <p className="text-xs text-red-500">Passwords do not match.</p>
            )}
          </div>

          <div className="space-y-2">
            <label className="text-xs font-semibold text-gray-700">
              SMTP Host
            </label>
            <Input
              value={formData.host}
              name="host"
              onChange={handleChange}
              placeholder="SMTP Host"
              required
            />
          </div>

          <div className="inline-flex items-center space-x-9">
            <div className="space-y-2">
              <label className="text-xs font-semibold text-gray-700">Port</label>
              <Input
                type="number"
                name="port"
                min="1"
                value={formData.port}
                onChange={handleChangePort}
                placeholder="Enter Port"
                className="appearance-none"
                required
              />
            </div>

            <div className="space-y-2">
              <label className="text-xs font-semibold text-gray-700">
                Secure
              </label>
              <div className="flex items-center">
                <button
                  type="button"
                  onClick={() =>
                    setFormData((prev) => ({ ...prev, secure: !prev.secure }))
                  }
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none ${formData.secure ? "bg-green-600" : "bg-gray-400"
                    }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${formData.secure ? "translate-x-6" : "translate-x-1"
                      }`}
                  />
                </button>
              </div>
            </div>
          </div>

          <div className="mt-3 space-y-2">
            <Button
              type="submit"
              className="w-full bg-primary mt-5"
              disabled={!isValid || !hasChanges}
            >
              {isEditMode ? "Update Mail Configuration" : "Add Mail Configuration"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
