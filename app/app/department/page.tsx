"use client";

import React, { useState, useEffect } from "react";
import {
  Plus,
  SquarePen,
  Trash2,
  ChevronLeft,
  ChevronRight,
} from "@/components/icons/list";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import apiClient from "@/lib/apiClient";
import { PackageInfo } from "@/types/interface-type";
import { toast } from "sonner";
import DeleteConfirm from "@/components/delete-dailog";
import ISPpackagesForm from "@/components/department/department-form";
import { useFetch } from "@/hooks/useFetchOnMount";
import { useAuth } from "@/context/AuthContext";
import { useDataStore } from "@/stores/useStorage";

export default function PackagesPage() {
  // Use centralized storage
  const {
    packages: pack,
    nasVlan,
    nasRole,
    loadingPackages: packLoading,
    fetchPackages,
    fetchNasVlans,
    fetchNasRoles,
    packagesFetched,
    nasVlanFetched,
    nasRoleFetched
  } = useDataStore();

  const [search, setSearch] = useState("");
  const [showDeleteDailog, setDeletetDailog] = useState(false);
  const [isAddOpen, setIsAddOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [selectedDep, setSelectedDep] = useState<PackageInfo>();
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedItemsPerPageValue, setSelectedItemsPerPageValue] =
    useState<string>("15");

  // Fetch data using centralized storage
  useEffect(() => {
    if (!packagesFetched) fetchPackages();
    if (!nasVlanFetched) fetchNasVlans();
    if (!nasRoleFetched) fetchNasRoles();
  }, [packagesFetched, nasVlanFetched, nasRoleFetched, fetchPackages, fetchNasVlans, fetchNasRoles]);

  const packagesList: PackageInfo[] = pack || [];
  const { setIsRefreshed } = useAuth();
  const filteredPackages = packagesList
    ? [...packagesList]
      .filter((pkg) =>
        pkg?.package_name?.toLowerCase().includes(search.toLowerCase())
      )
      .sort(
        (a, b) =>
          a?.package_name?.localeCompare(b?.package_name || "", undefined, {
            sensitivity: "base",
          }) || 0
      )
    : [];

  // Pagination logic
  const itemsPerPage =
    selectedItemsPerPageValue === "all"
      ? filteredPackages.length
      : parseInt(selectedItemsPerPageValue, 10);

  const totalPages =
    itemsPerPage === 0 ? 1 : Math.ceil(filteredPackages.length / itemsPerPage);

  const currentPackages = filteredPackages.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Handle items per page change
  const handleItemsPerPageChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    const value = e.target.value;
    setSelectedItemsPerPageValue(value);
    setCurrentPage(1);
  };

  // Reset to page 1 when search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [search]);

  const handleSubmit = async (packageData: any) => {
    try {
      const response = await apiClient.post("/package", packageData);
      toast.success("Department created sucessfully");
      setIsAddOpen(false);
      setIsRefreshed((prev: boolean) => !prev);
      return true;
    } catch (error) {
      console.error("Failed to create department:", error);
      toast.error(error.message || "Failed to create department");
      return false;
    }
  };

  const handleEditPackage = (pkg: PackageInfo) => {
    setSelectedDep(pkg);
    setIsEditOpen(true);
  };
  const handleEditSubmit = async (updatedPackage: any) => {
    const { id, ...payload } = updatedPackage;
    try {
      const response = await apiClient.patch(`/package/${id}`, payload);
      toast.success("Department has been updated");
      setIsEditOpen(false);
      setIsRefreshed((prev: boolean) => !prev);
      return true;
    } catch (err: any) {
      console.error("Failed to edit department:", err);
      toast.error(err.message || "Failed to edit the department information");
      return false;
    }
  };
  const handleDeleteClick = (pkg: PackageInfo) => {
    setSelectedDep(pkg);

    setDeletetDailog(true);
  };

  const handleDelete = async (id: number) => {
    try {
      await apiClient.delete(`/package/${id}`);
      toast.success("Department deleted successfully!");
      console.log("Package deleted successfully");
      setDeletetDailog(false);
      setIsRefreshed((prev) => !prev);
    } catch (err) {
      console.error("Failed to delete package:", err);
      toast.error(err.message || "Failed to delete department");
    }
  };

  return (
    <div className="p-5 sm:p-5 space-y-3">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center flex-wrap gap-4">
        <h1 className="text-lg sm:text-lg font-bold sm:mb-3 text-wrap">
          Department Management
        </h1>
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 w-full sm:w-auto ">
          <Input
            type="text"
            placeholder="Search Departments by Name"
            value={search}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setSearch(e.target.value)
            }
            className="w-full sm:w-[250px] p-1 border border-gray-300  px-3 focus:ring focus:border-blue-300 rounded-full"
          />
          <Button
            onClick={() => setIsAddOpen(true)}
            className="w-full bg-buttoncolor sm:w-auto rounded-full"
          >
            <Plus />
            Add Department
          </Button>
        </div>
      </div>

      <div className="bg-white rounded shadow p-4 overflow-x-auto">
        <div className="w-full mb-4">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mb-4">
            <div className="flex flex-col sm:flex-row items-center gap-4">
              <div className="bg-white px-3 py-1 rounded  border text-center">
                <h3 className="text-sm font-medium text-blue-700 ">
                  Total Department: {""}
                  <span className="text-sm font-bold text-blue-800">
                    {packagesList?.length}
                  </span>
                </h3>
              </div>

              {/* Search Input */}
            </div>

            {/* Right Side: Show entries dropdown */}
            <div className="flex items-center gap-2 text-xs">
              <span>Show</span>
              <select
                value={selectedItemsPerPageValue}
                onChange={handleItemsPerPageChange}
                className="border border-gray-300 rounded p-1.5 h-7 text-xs focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="15">15</option>
                <option value="30">30</option>
                <option value="50">50</option>
                <option value="100">100</option>
                <option value="all">All</option>
              </select>
              <span>entries</span>
            </div>
          </div>
        </div>
        <div className="w-full overflow-x-auto ">
          <table className="min-w-max w-full">
            <thead className="bg-gray-200 text-left text-xs uppercase align-middle">
              <tr>
                <th className="px-4 py-2 ">S.N.</th>
                <th className="px-4 py-2 ">Department</th>
                <th className="px-4 py-2 ">Status</th>
                <th className="px-4 py-2 ">VLAN</th>
                <th className="px-4 py-2 ">Role</th>
                <th className="px-4 py-2 ">Network Speed</th>
                <th className="px-4 py-2 text-end">Actions</th>
              </tr>
            </thead>
            <tbody>
              {packLoading ? (
                <tr>
                  <td colSpan={7} className="text-center py-8 text-gray-500">
                    Loading departments...
                  </td>
                </tr>
              ) : filteredPackages?.length === 0 ? (
                <tr>
                  <td
                    colSpan={7}
                    className="px-1 sm:px-2 py-8 text-center text-blue-900 text-sm"
                  >
                    Oops! No departments matched your search
                  </td>
                </tr>
              ) : (
                currentPackages?.map((pkg, index) => (
                  <tr key={pkg?.id} className="border-b hover:bg-gray-50">
                    <td className="px-4 py-1 text-xs">
                      {(currentPage - 1) * itemsPerPage + index + 1}
                    </td>
                    <td className="px-4 py-1 text-xs font-black">
                      {pkg?.package_name}
                    </td>
                    <td className="px-4 py-1 text-xs">
                      {" "}
                      {pkg?.status === "Active" && (
                        <span className="px-2 py-1 rounded text-xs bg-green-500 text-gray-100">
                          Active
                        </span>
                      )}
                      {pkg?.status === "Inactive" && (
                        <span className="px-2 py-1 rounded text-xs bg-red-200 text-red-700">
                          Inactive
                        </span>
                      )}
                    </td>

                    <td className="px-4 py-1 text-xs">
                      {nasVlan?.find((vlan) => vlan.id === pkg?.vlan)
                        ?.vlan_id || "N/A"}
                    </td>
                    <td className="px-4 py-1 text-xs">
                      {nasRole?.find((role) => role.id === Number(pkg?.role))
                        ?.name || "N/A"}
                    </td>
                    <td className="px-4 py-1 text-xs">
                      {pkg?.upload} / {pkg?.download} Mbps
                    </td>
                    <td className="px-4 py-1 text-xs text-end">
                      {/* Align actions to end */}
                      <div className="flex items-center justify-end gap-2">
                        {/* Use justify-end for actions */}
                        <Button
                          onClick={() => handleEditPackage(pkg)}
                          className="bg-blue-600 hover:bg-blue-700 text-white p-1 rounded h-7 w-7"
                        >
                          <SquarePen className="h-4 w-4" />
                        </Button>
                        <Button
                          onClick={() => handleDeleteClick(pkg)}
                          className="bg-red-500 hover:bg-red-600 text-white p-1 rounded h-7 w-7"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>

          {/* Pagination controls */}
          {totalPages > 1 && (
            <div className="flex items-center gap-1 justify-left mt-4">
              <Button
                className="rounded-md w-8 h-7"
                size="ss"
                onClick={() =>
                  setCurrentPage((p: number) => Math.max(p - 1, 1))
                }
                disabled={currentPage === 1}
              >
                <ChevronLeft />
              </Button>
              <span className="text-[12px]">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                className="rounded-md w-8 h-7"
                size="ss"
                onClick={() =>
                  setCurrentPage((p: number) => Math.min(p + 1, totalPages))
                }
                disabled={currentPage === totalPages}
              >
                <ChevronRight />
              </Button>
            </div>
          )}
        </div>
      </div>
      {showDeleteDailog && selectedDep && (
        <DeleteConfirm
          id={selectedDep?.id}
          name={""}
          paraValue="Department"
          value={selectedDep?.package_name}
          onClose={() => setDeletetDailog(false)}
          onDelete={handleDelete}
          loading={false}
        ></DeleteConfirm>
      )}
      {isAddOpen && (
        <ISPpackagesForm
          onSubmit={handleSubmit}
          onCancel={() => setIsAddOpen(false)}
        />
      )}
      {isEditOpen && (
        <ISPpackagesForm
          onSubmit={handleEditSubmit}
          onCancel={() => setIsEditOpen(false)}
          initialData={selectedDep}
          isEdit={true}
        />
      )}
    </div>
  );
}
