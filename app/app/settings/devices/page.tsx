"use client";

import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import apiClient from "@/lib/apiClient";
import {
  NAS,
  NASRole,
  NASRoleFormData,
  NASFormData,
  NASVlan,
  NASVlanFormData,
} from "@/types/interface-type";
import { toast } from "sonner";
import { SquarePen, Trash2, Eye, EyeOff } from "@/components/icons/list";
import DeleteConfirm from "@/components/delete-dailog";
import NASForm from "@/components/settings/devices/devices-form";
import { useAuth } from "@/context/AuthContext";
import { useFetch } from "@/hooks/useFetchOnMount";
import { Input } from "@/components/ui/input";
import NASRoleForm from "@/components/settings/devices/nasrole-form";
import NASVlanForm from "@/components/settings/devices/nasvlan-form";
import { useDataStore } from "@/stores/useStorage";

const TABS = ["Vlan", "Role", "Device"] as const;

const NasPage = () => {
  const [selectedNas, setSelectedNas] = useState(null);
  const [selectedNasRole, setSelectedNasRole] = useState(null);
  const [selectedNasVlan, setSelectedNasVlan] = useState(null);
  const [showSecretMap, setShowSecretMap] = useState<{
    [key: string]: boolean;
  }>({});
  const { isRefreshed, setIsRefreshed } = useAuth();
  const [isAddOpen, setIsAddOpen] = useState(false);
  const [isRoleAddOpen, setIsRoleAddOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [isRoleEditOpen, setIsRoleEditOpen] = useState(false);
  const [isVlanAddOpen, setIsVlanAddOpen] = useState(false);
  const [isVlanEditOpen, setIsVlanEditOpen] = useState(false);
  const [search, setSearch] = useState("");
  const [isDelete, setIsDelete] = useState(false);
  const [isDeleteRole, setIsDeleteRole] = useState(false);
  const [isDeleteVlan, setIsDeleteVlan] = useState(false);

  // Use centralized storage
  const {
    nasDevices: nasData,
    nasRole,
    nasVlan,
    loadingNasDevices: nasLoading,
    nasDevicesError: nasError,
    fetchNasDevices,
    fetchNasVlans,
    fetchNasRoles,
    nasDevicesFetched,
    nasVlanFetched,
    nasRoleFetched,
    setNasRole,
    setNasVlan
  } = useDataStore();

  // Fetch data using centralized storage
  useEffect(() => {
    if (!nasDevicesFetched) fetchNasDevices();
    if (!nasVlanFetched) fetchNasVlans();
    if (!nasRoleFetched) fetchNasRoles();
  }, [nasDevicesFetched, nasVlanFetched, nasRoleFetched, fetchNasDevices, fetchNasVlans, fetchNasRoles]);

  const [activeTab, setActiveTab] = useState("Vlan");
  const filteredDevices = nasData?.filter(
    (group) =>
      group?.nasname?.toLowerCase().includes(search.toLowerCase()) ||
      group?.name?.toLowerCase().includes(search.toLowerCase()) ||
      group?.vendor?.toLowerCase().includes(search.toLowerCase())
  );
  const filteredNasRole = nasRole?.filter((role) =>
    role?.name?.toLowerCase().includes(search.toLowerCase())
  );

  const filterVlan = nasVlan?.filter(
    (group) =>
      group?.name?.toLowerCase().includes(search.toLowerCase()) ||
      group?.vlan_id?.toString().includes(search.toLowerCase())
  );

  const handleEditSubmit = async (updatedNAS: NASFormData) => {
    const { id, ...payload } = updatedNAS || {};
    try {
      const response = await apiClient.patch(`/nas/${id}`, payload);
      toast.success("Device has been successfully updated!!");
      setIsEditOpen(false);
      setIsRefreshed((prev) => !prev);
    } catch (error) {
      console.error("Failed to update device:", error);
      toast.error("Failed to update Device. Please try again.");
    }
  };

  const handleEditRoleSubmit = async (updatedNASRole: NASRoleFormData) => {
    const { id, ...payload } = updatedNASRole || {};
    const payloads = {
      id: id,
      name: payload.name,
    };
    try {
      const response = await apiClient.patch(`/nas/role`, payloads);
      toast.success("Role has been successfully updated!!");
      setIsRoleEditOpen(false);
      setIsRefreshed((prev) => !prev);
    } catch (error) {
      console.error("Failed to update role:", error);
      toast.error("Failed to update Role. Please try again.");
    }
  };

  const handleEditVlanSubmit = async (updatedNASVlan: NASVlanFormData) => {
    const { id, ...payload } = updatedNASVlan || {};
    const payloads = {
      id: id,
      name: payload.name,
      vlan_id: payload.vlan_id,
    };
    try {
      const response = await apiClient.patch(`/nas/vlan`, payloads);
      toast.success("Vlan has been successfully updated!!");
      setIsVlanEditOpen(false);
      setIsRefreshed((prev) => !prev);
    } catch (error) {
      console.error("Failed to update vlan:", error);
      toast.error("Failed to update Vlan. Please try again.");
    }
  };

  const handleSubmit = async (nasData: NASFormData) => {
    try {
      const response = await apiClient.post("/nas", nasData);
      toast.success("Device created successfully");
      setIsAddOpen(false);
      setIsRefreshed((prev) => !prev);
    } catch (error) {
      console.error("Failed to create device:", error);
      toast.error("Failed to create Device. Please try again.");
    }
  };

  const handleRoleSubmit = async (nasRoleData: NASRoleFormData) => {
    try {
      const response = await apiClient.post("/nas/role", nasRoleData);
      toast.success("Role created successfully");
      setIsRoleAddOpen(false);
      setIsRefreshed((prev) => !prev);
    } catch (error) {
      console.error("Failed to create role:", error);
      toast.error("Failed to create Role. Please try again.");
    }
  };

  const handleVlanSubmit = async (nasVlanData: NASVlanFormData) => {
    try {
      const response = await apiClient.post("/nas/vlan", nasVlanData);
      toast.success("Vlan created successfully");
      setIsVlanAddOpen(false);
      setIsRefreshed((prev) => !prev);
    } catch (error) {
      console.error("Failed to create vlan:", error);
      toast.error("Failed to create Vlan. Please try again.");
    }
  };

  const handleDeleteClick = (nas) => {
    setSelectedNas(nas);
    setIsDelete(true);
  };

  const handleDeleteRoleClick = (nasRole) => {
    setSelectedNasRole(nasRole);
    setIsDeleteRole(true);
  };

  const handleDeleteVlanClick = (nasVlan) => {
    setSelectedNasVlan(nasVlan);
    setIsDeleteVlan(true);
  };

  const handleDeleteNAS = async (id: number) => {
    try {
      await apiClient.delete(`/nas/${id}`);
      toast.success("Device deleted successfully");
      setIsRefreshed((prev) => !prev);
      setShowSecretMap((prevMap) => {
        const newMap = { ...prevMap };
        delete newMap[id];
        return newMap;
      });
    } catch (error) {
      console.error("Failed to delete device:", error);
      toast.error("Failed to delete device. Please try again.");
    }
  };

  const handleDeleteNASRole = async (id: number) => {
    try {
      await apiClient.delete(`/nas/role/${id}`);
      toast.success("Role deleted successfully");
      setIsRefreshed((prev) => !prev);
    } catch (error) {
      console.error("Failed to delete role:", error);
      toast.error("Failed to delete role. Please try again.");
    }
  };

  const handleDeleteNASVlan = async (id: number) => {
    try {
      await apiClient.delete(`/nas/vlan/${id}`);
      toast.success("Vlan deleted successfully");
      setIsRefreshed((prev) => !prev);
    } catch (error) {
      console.error("Failed to delete vlan:", error);
      toast.error("Failed to delete vlan. Please try again.");
    }
  };

  const handleEditNAS = (nas: NAS) => {
    setSelectedNas(nas);
    setIsEditOpen(true); // Using setIsEditOpen
  };

  const handleEditNASRole = (nas: NASRole) => {
    setSelectedNasRole(nas);
    setIsRoleEditOpen(true); // Using setIsEditOpen
  };

  const handleEditNASVlan = (nas: NASVlan) => {
    setSelectedNasVlan(nas);
    setIsVlanEditOpen(true); // Using setIsEditOpen
  };

  const toggleSecretVisibility = (id: string) => {
    setShowSecretMap((prevMap) => ({
      ...prevMap,
      [id]: !prevMap[id],
    }));
  };

  const getNasRole = async () => {
    try {
      const response = await apiClient.get("/nas/role");
      setNasRole(response?.data || []);
    } catch (error) {
      console.error("Failed to fetch NAS:", error);
      toast.error(error.message || "Failed to fetch NAS");
    }
  };
  const getNasVlan = async () => {
    try {
      const response = await apiClient.get("/nas/vlan");
      setNasVlan(response?.data || []);
    } catch (error) {
      console.error("Failed to fetch NAS:", error);
      toast.error(error.message || "Failed to fetch NAS");
    }
  };

  useEffect(() => {
    getNasRole();
    getNasVlan();
  }, [isRefreshed]);

  return (
    <div className="p-5 sm:p-5 space-y-3">
      {/* Navigation Tabs */}

      <div className="flex flex-col sm:flex-row gap-2 w-full ">
        {TABS.map((tab) => (
          <button
            key={tab}
            onClick={() => {
              setActiveTab(tab);
              setSearch("");
            }}
            className={`px-4 py-1.5 rounded-md border w-full sm:w-auto  ${activeTab === tab
                ? "bg-blue-600 text-white"
                : "border-gray-300 text-gray-700 bg-white"
              }`}
          >
            {tab}
          </button>
        ))}
      </div>

      {activeTab === "Vlan" && (
        <>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center flex-wrap gap-4">
            <h1 className="text-lg sm:text-lg font-bold sm:mb-3 text-wrap">
              Vlan Management
            </h1>
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 w-full sm:w-auto">
              <Input
                type="text"
                placeholder="Search..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="w-full sm:w-[250px] p-1 border border-gray-300 rounded-full px-3 focus:ring focus:border-blue-300"
              />
              <Button
                className="w-full bg-buttoncolor sm:w-auto px-3 rounded-full"
                onClick={() => setIsVlanAddOpen(true)}
              >
                Add Vlan
              </Button>
            </div>
          </div>
          <div className="bg-white rounded shadow p-4 overflow-x-auto">
            <div className="w-full overflow-x-auto ">
              <table className="min-w-max w-full">
                <thead className="bg-gray-200 text-left text-xs uppercase align-middle">
                  <tr>
                    <th className="px-4 py-2 ">S.N.</th>
                    <th className="px-4 py-2 "> Name</th>
                    <th className="px-4 py-2 ">VLAN ID</th>
                    <th className="px-4 py-2 text-end">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filterVlan?.length === 0 ? (
                    <tr>
                      <td
                        colSpan={7}
                        className="px-1 sm:px-2 py-8 text-center text-blue-900 text-sm"
                      >
                        Oops! No Vlan matched your search
                      </td>
                    </tr>
                  ) : (
                    filterVlan?.map((item: NASVlan, index: number) => (
                      <tr key={item?.id} className="border-b hover:bg-gray-50">
                        <td className="px-4 py-1 text-xs">{index + 1}</td>
                        <td className="px-4 py-1 text-xs">{item?.name}</td>
                        <td className="px-4 py-1 text-xs">{item?.vlan_id}</td>
                        <td className="px-4 py-1 text-xs">
                          <div className="flex items-center justify-end gap-1">
                            <Button
                              onClick={() => handleEditNASVlan(item)}
                              className="bg-blue-600 hover:bg-blue-700 text-white p-1 rounded h-7 w-7"
                            >
                              <SquarePen className="h-4 w-4" />{" "}
                            </Button>
                            <Button
                              onClick={() => handleDeleteVlanClick(item)}
                              className="bg-red-500 hover:bg-red-600 text-white p-1 rounded h-7 w-7"
                            >
                              <Trash2 />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
            {isVlanAddOpen && (
              <NASVlanForm
                onSubmit={handleVlanSubmit}
                onCancel={() => setIsVlanAddOpen(false)}
                isEdit={false}
              />
            )}
            {isVlanEditOpen && ( // Correctly checking for isEditOpen
              <NASVlanForm
                initialData={selectedNasVlan}
                onSubmit={handleEditVlanSubmit}
                onCancel={() => setIsVlanEditOpen(false)} // Correctly using setIsEditOpen
                isEdit={true}
              />
            )}
          </div>
          {isDeleteVlan && (
            <DeleteConfirm
              name={""}
              id={selectedNasVlan?.id}
              paraValue="Vlan"
              value={selectedNasVlan?.name}
              onDelete={handleDeleteNASVlan}
              onClose={() => setIsDeleteVlan(false)}
              loading={false}
            ></DeleteConfirm>
          )}
        </>
      )}

      {activeTab === "Role" && (
        <>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center flex-wrap gap-4">
            <h1 className="text-lg sm:text-lg font-bold sm:mb-3 text-wrap">
              Role Management
            </h1>
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 w-full sm:w-auto">
              <Input
                type="text"
                placeholder="Search..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="w-full sm:w-[250px] p-1 border border-gray-300 rounded-full px-3 focus:ring focus:border-blue-300"
              />
              <Button
                className="w-full bg-buttoncolor sm:w-auto px-3 rounded-full"
                onClick={() => setIsRoleAddOpen(true)}
              >
                Add Role
              </Button>
            </div>
          </div>
          <div className="bg-white rounded shadow p-4 overflow-x-auto">
            <div className="w-full overflow-x-auto ">
              <table className="min-w-max w-full">
                <thead className="bg-gray-200 text-left text-xs uppercase align-middle">
                  <tr>
                    <th className="px-4 py-2 ">S.N.</th>
                    <th className="px-4 py-2 "> Name</th>
                    <th className="px-4 py-2 text-end">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredNasRole?.length === 0 ? (
                    <tr>
                      <td
                        colSpan={7}
                        className="px-1 sm:px-2 py-8 text-center text-blue-900 text-sm"
                      >
                        Oops! No role matched your search
                      </td>
                    </tr>
                  ) : (
                    filteredNasRole.map((item: NASRole, index: number) => (
                      <tr key={item?.id} className="border-b hover:bg-gray-50">
                        <td className="px-4 py-1 text-xs">{index + 1}</td>
                        <td className="px-4 py-1 text-xs">{item?.name}</td>
                        <td className="px-4 py-1 text-xs">
                          <div className="flex items-center justify-end gap-1">
                            <Button
                              onClick={() => handleEditNASRole(item)}
                              className="bg-blue-600 hover:bg-blue-700 text-white p-1 rounded h-7 w-7"
                            >
                              <SquarePen className="h-4 w-4" />{" "}
                            </Button>
                            <Button
                              onClick={() => handleDeleteRoleClick(item)}
                              className="bg-red-500 hover:bg-red-600 text-white p-1 rounded h-7 w-7"
                            >
                              <Trash2 />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
            {isRoleAddOpen && (
              <NASRoleForm
                onSubmit={handleRoleSubmit}
                onCancel={() => setIsRoleAddOpen(false)}
                isEdit={false}
              />
            )}
            {isRoleEditOpen && ( // Correctly checking for isEditOpen
              <NASRoleForm
                initialData={selectedNasRole}
                onSubmit={handleEditRoleSubmit}
                onCancel={() => setIsRoleEditOpen(false)} // Correctly using setIsEditOpen
                isEdit={true}
              />
            )}
          </div>
          {isDeleteRole && (
            <DeleteConfirm
              id={selectedNasRole?.id}
              paraValue="Role"
              value={selectedNasRole?.name}
              onDelete={handleDeleteNASRole}
              onClose={() => setIsDeleteRole(false)}
              loading={false}
            ></DeleteConfirm>
          )}
        </>
      )}

      {/* Content based on active tab */}
      {activeTab === "Device" && (
        <>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center flex-wrap gap-4">
            <h1 className="text-lg sm:text-lg font-bold sm:mb-3 text-wrap">
              Device Management
            </h1>
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 w-full sm:w-auto">
              <Input
                type="text"
                placeholder="Search ..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="w-full sm:w-[250px] p-1 border border-gray-300 rounded-full px-3 focus:ring focus:border-blue-300"
              />
              <Button
                className="w-full bg-buttoncolor sm:w-auto px-3 rounded-full"
                onClick={() => setIsAddOpen(true)}
              >
                Add Device
              </Button>
            </div>
          </div>
          <div className="bg-white rounded shadow p-4 overflow-x-auto">
            <div className="w-full overflow-x-auto ">
              <table className="min-w-max w-full">
                <thead className="bg-gray-200 text-left text-xs uppercase align-middle">
                  <tr>
                    <th className="px-4 py-2 ">S.N.</th>
                    <th className="px-4 py-2 ">Device IP</th>
                    <th className="px-4 py-2 "> Name</th>
                    <th className="px-4 py-2 ">Description</th>
                    <th className="px-4 py-2 ">Vendor</th>
                    <th className="px-4 py-2 ">Secret</th>
                    <th className="px-4 py-2 text-end">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredDevices?.length === 0 ? (
                    <tr>
                      <td
                        colSpan={7}
                        className="px-1 sm:px-2 py-8 text-center text-blue-900 text-sm"
                      >
                        Oops! No Device matched your search
                      </td>
                    </tr>
                  ) : (
                    filteredDevices.map((item: NAS, index: number) => (
                      <tr key={item?.id} className="border-b hover:bg-gray-50">
                        <td className="px-4 py-1 text-xs">{index + 1}</td>
                        <td className="px-4 py-1 text-xs">{item?.nasname}</td>
                        <td className="px-4 py-1 text-xs">{item?.name}</td>
                        <td className="px-4 py-1 text-xs">
                          {item?.description}
                        </td>
                        <td className="px-4 py-1 text-xs">{item?.vendor}</td>
                        <td className="px-4 py-1 text-xs">
                          {item?.secret ? (
                            <div className="flex items-center font-mono w-25 space-x-2">
                              <span className="truncate inline-block max-w-[5.5rem]">
                                {showSecretMap[item?.id!]
                                  ? item?.secret
                                  : "•••••••••••"}
                              </span>
                              <button
                                onClick={() =>
                                  toggleSecretVisibility(item?.id!)
                                }
                                className="text-gray-500 hover:text-gray-700 focus:outline-none h-6 w-6"
                                aria-label={
                                  showSecretMap[item?.id!]
                                    ? "Hide secret"
                                    : "Show secret"
                                }
                                aria-pressed={showSecretMap[item?.id!]}
                              >
                                {showSecretMap[item?.id!] ? (
                                  <EyeOff />
                                ) : (
                                  <Eye />
                                )}
                              </button>
                            </div>
                          ) : (
                            "N/A"
                          )}
                        </td>
                        <td className="px-4 py-1 text-xs">
                          <div className="flex items-center justify-end gap-1">
                            <Button
                              onClick={() => handleEditNAS(item)}
                              className="bg-blue-600 hover:bg-blue-700 text-white p-1 rounded h-7 w-7"
                            >
                              <SquarePen className="h-4 w-4" />{" "}
                            </Button>
                            <Button
                              onClick={() => handleDeleteClick(item)}
                              className="bg-red-500 hover:bg-red-600 text-white p-1 rounded h-7 w-7"
                            >
                              <Trash2 />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
            {isAddOpen && (
              <NASForm
                onSubmit={handleSubmit}
                onCancel={() => setIsAddOpen(false)}
                isEdit={false}
              />
            )}
            {isEditOpen && ( // Correctly checking for isEditOpen
              <NASForm
                initialData={selectedNas}
                onSubmit={handleEditSubmit}
                onCancel={() => setIsEditOpen(false)} // Correctly using setIsEditOpen
                isEdit={true}
              />
            )}
          </div>
          {isDelete && (
            <DeleteConfirm
              id={selectedNas?.id}
              paraValue="Device"
              value={selectedNas?.name}
              onDelete={handleDeleteNAS}
              onClose={() => setIsDelete(false)}
              loading={false}
            ></DeleteConfirm>
          )}
        </>
      )}
    </div>
  );
};

export default NasPage;
