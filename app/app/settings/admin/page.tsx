"use client";
import React, { useState } from "react";
import { Mail, Building2, Settings } from "lucide-react";
import { toast } from "sonner";
import apiClient from "@/lib/apiClient";
import { useAuth } from "@/context/AuthContext";

import { useFetch } from "@/hooks/useFetchOnMount";
import ManageMail from "@/components/settings/admin/mail-management";
import ManageOrganization from "@/components/settings/admin/organization-management";
import VlanRoleManagement from "@/components/settings/admin/vlan-role";

export default function Admin() {
  const [activeTab, setActiveTab] = useState<"mail" | "organization" | "attributes">("mail");
  const { data: fetchedData, loading } = useFetch("/admin?func=email");
  const { setIsRefreshed, org_id } = useAuth();
  const handleMailSubmit = async (user: any) => {
    const { id, ...payload } = user;
    try {
      // await apiClient.patch(`/user/${id}`, payload);
      toast.success("Mail updated successfully");
      setIsRefreshed((prev) => !prev);
    } catch (error) {
      console.error("Failed to update mail:", error);
      toast.error("Failed to update mail");
    }
  };

  const handleVlanRoleSubmit = async (user: any) => {
    const { role, vlan } = user;
    const payloads = {
      "key": "default_attribute",
      "value": {
        "vlan": vlan,
        "role": role
      }
    };
    console.log(payloads);
    try {
      // await apiClient.post(`/user/default`, { payloads });
      toast.success("Vlan and Role updated successfully");
      setIsRefreshed((prev) => !prev);
    } catch (error) {
      console.error("Failed to update vlan and role:", error);
      toast.error("Failed to update vlan and role");
    }
  };

  const handleOrgSubmit = async (updatedOrg: any) => {
    const { id, ...payload } = updatedOrg;
    try {
      await apiClient.patch(`/organization/${id}`, payload);
      toast.success("Organization updated successfully");
      setIsRefreshed((prev) => !prev);
    } catch (error) {
      console.error("Failed to update organization:", error);
      toast.error("Failed to update organization");
    }
  };

  const handleTabChange = (tab: "mail" | "organization" | "attributes") => {
    setActiveTab(tab);
  };

  if (loading || !fetchedData) {
    return <div className="text-center py-10">Loading email config...</div>;
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Admin Settings</h1>
          <p className="text-gray-600 mt-1">Manage system configuration and settings</p>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: "mail", label: "Mail", icon: Mail },
            { id: "organization", label: "Organization", icon: Building2 },
            { id: "attributes", label: "Attributes", icon: Settings }
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => handleTabChange(tab.id as any)}
                className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm ${activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
              >
                <Icon className="h-5 w-5" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === "mail" && (
          <ManageMail
            initialData={fetchedData && fetchedData.length > 0 ? fetchedData[0].value : null}
            onSubmit={handleMailSubmit}
          />
        )}

        {activeTab === "organization" && (
          <ManageOrganization
            organization={{ id: org_id, name: "" }}
            onSubmit={handleOrgSubmit}
          />
        )}

        {activeTab === "attributes" && (
          <VlanRoleManagement
            onSubmit={handleVlanRoleSubmit}
          />
        )}
      </div>
    </div>
  );
}
